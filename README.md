# Flash Ops - 管理后台系统

基于 NestJS 和 AdminJS 构建的现代化管理后台系统，提供用户管理、服务管理、发票管理等功能。

## 🚀 项目概述

Flash Ops 是一个功能强大的管理后台系统，专为企业级应用设计。该系统集成了 AdminJS 7.x，提供直观的管理界面和丰富的数据管理功能。

### 主要功能

- **用户管理**：完整的用户 CRUD 操作，包括密码管理
- **服务管理**：服务配置、SuperAPI 集成、流量监控
- **发票管理**：发票创建、跟踪、项目管理
- **数据分析**：用户行为分析、服务使用统计
- **权限控制**：基于角色的访问控制
- **邮件通知**：自动化邮件通知系统

## 🛠️ 技术栈

### 后端框架
- **NestJS** - 企业级 Node.js 框架
- **TypeScript** - 类型安全的 JavaScript
- **TypeORM** - 对象关系映射框架
- **MySQL** - 关系型数据库

### 管理界面
- **AdminJS 7.x** - 自动化管理界面生成
- **React** - 用于自定义组件
- **AdminJS Design System** - 统一的设计系统

### 部署与运维
- **Docker** - 容器化部署
- **Caddy** - 现代化 Web 服务器
- **Google Cloud Build** - CI/CD 流程
- **Kubernetes** - 容器编排

## 📦 项目结构

```
src/
├── admin/                    # AdminJS 配置和自定义组件
│   ├── components/          # 自定义 React 组件
│   │   ├── UpdatePassword.tsx      # 密码更新组件
│   │   ├── EditSuperAPI.tsx       # SuperAPI 编辑组件
│   │   ├── UserInfoCard.tsx       # 用户信息卡片
│   │   └── ...
│   ├── models/             # 数据模型定义
│   │   ├── user.entity.ts          # 用户实体
│   │   ├── service.entity.ts       # 服务实体
│   │   ├── invoice.entity.ts       # 发票实体
│   │   └── ...
│   ├── resources/          # AdminJS 资源配置
│   │   ├── user-resource.ts        # 用户资源配置
│   │   ├── service-resource.ts     # 服务资源配置
│   │   └── ...
│   └── types/              # TypeScript 类型定义
├── envs/                   # 环境配置文件
│   ├── dev.env
│   ├── uat.env
│   └── prod.env
├── k8s/                    # Kubernetes 配置
│   ├── prod/
│   └── uat/
└── main.ts                 # 应用入口点
```

## 🚀 快速开始

### 环境要求

- Node.js 20.x+
- MySQL 8.0+
- npm 或 yarn

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd foxit/ops
   ```

2. **安装依赖**
   ```bash
   npm ci --legacy-peer-deps
   ```

3. **配置环境变量**
   ```bash
   # 复制环境配置文件
   cp envs/dev.env .env
   
   # 编辑环境变量
   nano .env
   ```

4. **数据库配置**
   ```bash
   # 确保 MySQL 服务运行
   # 创建数据库
   mysql -u root -p -e "CREATE DATABASE flash_ops;"
   ```

5. **启动开发服务器**
   ```bash
   npm run start:dev
   ```

6. **访问管理界面**
   ```
   http://localhost:3002/admin
   ```

### 环境变量说明

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=flash_ops

# 应用配置
PORT=3002
NODE_ENV=development

# AdminJS 配置
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure_password

# SuperAPI 配置
SUPERAPI_BASE_URL=https://api.superapi.com
SUPERAPI_TOKEN=your_api_token
```

## 🐳 Docker 部署

### 构建镜像
```bash
docker build -t flash-ops:latest .
```

### 运行容器
```bash
docker run -d \
  --name flash-ops \
  -p 80:80 \
  -e DB_HOST=your_db_host \
  -e DB_PASSWORD=your_db_password \
  flash-ops:latest
```

### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "80:80"
    environment:
      - DB_HOST=mysql
      - DB_PASSWORD=secure_password
    depends_on:
      - mysql
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: secure_password
      MYSQL_DATABASE: flash_ops
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
```

## 🔧 开发指南

### 添加新的管理资源

1. **创建实体模型**
   ```typescript
   // src/admin/models/example.entity.ts
   import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

   @Entity()
   export class Example {
     @PrimaryGeneratedColumn()
     id: number;

     @Column()
     name: string;
   }
   ```

2. **配置资源**
   ```typescript
   // src/admin/resources/example-resource.ts
   import { Example } from '../models/example.entity.js';

   export const ExampleResource = {
     resource: Example,
     options: {
       properties: {
         id: {
           isVisible: { list: true, filter: true, show: true, edit: false }
         },
         name: {
           isVisible: { list: true, filter: true, show: true, edit: true }
         }
       }
     }
   };
   ```

### 创建自定义组件

1. **组件文件**
   ```tsx
   // src/admin/components/CustomComponent.tsx
   import React from 'react';
   import { Box, Text } from '@adminjs/design-system';

   const CustomComponent: React.FC<any> = ({ record, resource }) => {
     return (
       <Box p="lg">
         <Text>自定义组件内容</Text>
       </Box>
     );
   };

   export default CustomComponent;
   ```

2. **注册组件**
   ```typescript
   // src/admin/component-loader.ts
   const Components = {
     // ...existing components
     CustomComponent: componentLoader.add('CustomComponent', './components/CustomComponent'),
   }
   ```

### 自定义动作

```typescript
// 在资源配置中添加自定义动作
actions: {
  customAction: {
    actionType: 'record',
    component: Components.CustomComponent,
    handler: async (request, response, context) => {
      // 处理逻辑
      return {
        record: context.record?.toJSON(context.currentAdmin),
        notice: {
          message: '操作成功',
          type: 'success'
        }
      };
    },
  }
}
```

## 📚 API 文档

### 用户管理 API

- `GET /admin/api/resources/User` - 获取用户列表
- `POST /admin/api/resources/User` - 创建用户
- `PUT /admin/api/resources/User/{id}` - 更新用户
- `DELETE /admin/api/resources/User/{id}` - 删除用户
- `POST /admin/api/resources/User/records/{id}/update-password` - 更新密码

### 服务管理 API

- `GET /admin/api/resources/Service` - 获取服务列表
- `POST /admin/api/resources/Service/records/{id}/editSuperapi` - 编辑 SuperAPI

## 🔒 安全配置

### 密码策略
- 最小长度：8 位字符
- 使用 bcrypt 进行哈希加密
- 支持密码强度验证

### 访问控制
- 基于会话的身份验证
- 角色权限管理
- API 访问限制

### 数据保护
- 敏感数据加密存储
- SQL 注入防护
- XSS 攻击防护

## 📈 监控与日志

### 应用监控
- 健康检查端点
- 性能指标收集
- 错误日志记录

### 数据库监控
- 连接池状态
- 查询性能分析
- 慢查询日志

## 🚀 部署指南

### 生产环境部署

1. **环境准备**
   ```bash
   # 使用生产环境配置
   cp envs/prod.env .env
   ```

2. **构建应用**
   ```bash
   npm run build
   ```

3. **启动服务**
   ```bash
   npm run start:prod
   ```

### Kubernetes 部署

```bash
# 部署到生产环境
kubectl apply -f k8s/prod/

# 部署到 UAT 环境
kubectl apply -f k8s/uat/
```

### Google Cloud Build

项目集成了 Google Cloud Build 进行 CI/CD：

- `cloudbuild.yaml` - 生产环境构建配置
- `cloudbuild-uat.yaml` - UAT 环境构建配置

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接配置
   - 确认网络连通性

2. **AdminJS 组件错误**
   - 检查组件导入路径
   - 验证 TypeScript 类型
   - 清除构建缓存

3. **Docker 构建失败**
   - 更新 Node.js 版本
   - 检查依赖兼容性
   - 清理 Docker 缓存

### 调试技巧

```bash
# 启用调试模式
npm run start:debug

# 查看应用日志
docker logs flash-ops

# 数据库调试
npm run typeorm:log
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范

- 使用 ESLint 进行代码检查
- 遵循 TypeScript 最佳实践
- 编写单元测试
- 更新文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 👥 团队

- **开发团队** - Foxit Development Team
- **维护者** - [维护者姓名](mailto:<EMAIL>)

## 📞 支持

如有问题或需要支持，请：

1. 查看 [FAQ](docs/FAQ.md)
2. 提交 [Issue](../../issues)
3. 联系技术支持：<EMAIL>

---

**Flash Ops** - 让管理更简单，让数据更智能 ✨
