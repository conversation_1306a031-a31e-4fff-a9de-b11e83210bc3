steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: [ 'build', '-t', 'gcr.io/$PROJECT_ID/ops:$COMMIT_SHA', '.' ]
    id: build_image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/ops:$COMMIT_SHA']
    id: push_image
  - name: "ubuntu"
    args: ["sed", "-i" ,"s/latest/$COMMIT_SHA/", "k8s/prod/deployment.yaml"]
    id: update_config
  - name: "bitnami/kubectl"
    waitFor:
      - push_image
    args:
      - --server=$_CLUSTER_URL
      - --token=$_CLUSTER_TOKEN
      - --insecure-skip-tls-verify=true
      - apply
      - -f
      - k8s/prod/deployment.yaml
    id: kube_deploy
timeout: '1600s'
