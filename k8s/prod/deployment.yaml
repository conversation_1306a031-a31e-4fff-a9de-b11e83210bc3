kind: Deployment
apiVersion: apps/v1
metadata:
  labels:
    k8s-app: ops
  name: ops
  namespace: flash
spec:
  strategy:
    type: RollingUpdate
  selector:
    matchLabels:
      k8s-app: ops
  template:
    metadata:
      labels:
        k8s-app: ops
    spec:
      imagePullSecrets:
        - name: gcr-flash
      containers:
        - name: ops
          image: gcr.io/flashvpn-253908/ops:latest
          env:
            - name: NODE_ENV
              value: prod
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: dbsecret
                  key: WHMCS_DB_HOST
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: dbsecret
                  key: WHMCS_DB_USER
            - name: DB_PASS
              valueFrom:
                secretKeyRef:
                  name: dbsecret
                  key: WHMCS_DB_PASS
            - name: API_DB_NAME
              value: api
            - name: NOTIFY_DB_NAME
              value: notify
            - name: WHMCS_DB_NAME
              value: whmcs
          readinessProbe:
            httpGet:
              path: /
              port: 3002
            initialDelaySeconds: 3
            periodSeconds: 3
---

kind: Service
apiVersion: v1
metadata:
  name: ops
  namespace: flash
  labels:
    k8s-app: ops
spec:
  ports:
    - name: ops-tcp-80
      protocol: TCP
      port: 80
      targetPort: 80
  selector:
    k8s-app: ops
  type: ClusterIP
