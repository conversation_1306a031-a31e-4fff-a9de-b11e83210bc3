import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import styled from 'styled-components';

const HeaderContainer = styled(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
`;

interface CardHeaderProps {
  title: string;
  subtitle?: string;
  children?: React.ReactNode;
}

const CardHeader: React.FC<CardHeaderProps> = ({ title, subtitle, children }) => {
  return (
    <HeaderContainer>
      <Box>
        <Text variant="lg" fontWeight="bold">
          {title}
        </Text>
        {subtitle && (
          <Text variant="sm" color="grey60">
            {subtitle}
          </Text>
        )}
      </Box>
      {children && <Box>{children}</Box>}
    </HeaderContainer>
  );
};

export default CardHeader;
