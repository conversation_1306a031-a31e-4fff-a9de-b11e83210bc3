import React from 'react';

interface DatePaidDisplayProps {
  record?: any;
  property?: any;
}

const DatePaidDisplay: React.FC<DatePaidDisplayProps> = ({ record, property }) => {
  const value = record?.params?.[property?.path] || record?.params?.[property?.name];
  
  const formatDatePaid = (val: any): string => {
    if (!val) return '-';
    
    // 检查是否是无效的日期字符串或默认的"未支付"日期
    if (typeof val === 'string' && (
      val.includes('NaN') ||
      val === '0NaN-aN-aN' ||
      val.includes('NaN:NaN') ||
      val === '0000-00-00' ||
      val === '0000-00-00 00:00:00' ||
      val === '1970-01-01' ||
      val.startsWith('1970-01-01')
    )) {
      return '-';
    }
    
    // 检查是否是空的日期对象或无效日期
    if (val instanceof Date) {
      if (isNaN(val.getTime()) || val.getTime() === 0) {
        return '-';
      }
      // 检查是否是 1970-01-01（默认的"未支付"日期）
      if (val.getFullYear() === 1970 && val.getMonth() === 0 && val.getDate() === 1) {
        return '-';
      }
    }

    try {
      const date = new Date(val);
      if (isNaN(date.getTime()) || date.getTime() === 0) {
        return '-';
      }
      // 检查是否是 1970-01-01（默认的"未支付"日期）
      if (date.getFullYear() === 1970 && date.getMonth() === 0 && date.getDate() === 1) {
        return '-';
      }
      return date.toLocaleDateString();
    } catch (e) {
      return '-';
    }
  };

  return <span>{formatDatePaid(value)}</span>;
};

export default DatePaidDisplay;
