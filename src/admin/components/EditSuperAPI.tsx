import React, { useEffect, useState } from 'react';
import {
  Box,
  Text,
  Loader,
  Button,
  Input,
  FormGroup,
  Label,
  MessageBox,
  Badge,
  Icon,
  Select
} from '@adminjs/design-system';
import { ApiClient } from 'adminjs';

const InfoCard = ({ children, title, variant = 'default', icon }) => {
  const getCardStyle = () => {
    const baseStyle = {
      background: 'white',
      border: '1px solid #e9ecef',
      borderRadius: '12px',
      padding: '0',
      marginBottom: '24px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
      transition: 'all 0.3s ease'
    };

    if (variant === 'primary') {
      return {
        ...baseStyle,
        border: '1px solid #007bff',
        boxShadow: '0 4px 20px rgba(0,123,255,0.15)'
      };
    }

    if (variant === 'success') {
      return {
        ...baseStyle,
        border: '1px solid #28a745',
        boxShadow: '0 4px 20px rgba(40,167,69,0.15)'
      };
    }

    if (variant === 'warning') {
      return {
        ...baseStyle,
        border: '1px solid #ffc107',
        boxShadow: '0 4px 20px rgba(255,193,7,0.15)'
      };
    }

    if (variant === 'danger') {
      return {
        ...baseStyle,
        border: '1px solid #dc3545',
        boxShadow: '0 4px 20px rgba(220,53,69,0.15)'
      };
    }

    return baseStyle;
  };

  const getHeaderStyle = () => {
    const baseStyle = {
      padding: '20px 24px',
      borderBottom: '1px solid #e9ecef',
      borderRadius: '12px 12px 0 0',
      display: 'flex',
      alignItems: 'center',
      gap: '12px'
    };

    if (variant === 'primary') {
      return {
        ...baseStyle,
        background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
        color: 'white',
        borderBottom: '1px solid #007bff'
      };
    }

    if (variant === 'success') {
      return {
        ...baseStyle,
        background: 'linear-gradient(135deg, #28a745 0%, #1e7e34 100%)',
        color: 'white',
        borderBottom: '1px solid #28a745'
      };
    }

    if (variant === 'warning') {
      return {
        ...baseStyle,
        background: 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)',
        color: '#212529',
        borderBottom: '1px solid #ffc107'
      };
    }

    if (variant === 'danger') {
      return {
        ...baseStyle,
        background: 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)',
        color: 'white',
        borderBottom: '1px solid #dc3545'
      };
    }

    return {
      ...baseStyle,
      background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)'
    };
  };

  return (
    <Box style={getCardStyle()}>
      {title && (
        <Box style={getHeaderStyle()}>
          {icon && (
            <Icon 
              icon={icon} 
              style={{ 
                fontSize: '20px',
                color: variant === 'warning' ? '#212529' : 
                       (variant === 'primary' || variant === 'success' || variant === 'danger') ? 'white' : '#495057'
              }} 
            />
          )}
          <Text variant="h6" style={{ 
            margin: 0, 
            fontWeight: 600, 
            color: variant === 'warning' ? '#212529' : 
                   (variant === 'primary' || variant === 'success' || variant === 'danger') ? 'white' : '#495057'
          }}>
            {title}
          </Text>
        </Box>
      )}
      <Box style={{ padding: '24px' }}>
        {children}
      </Box>
    </Box>
  );
};

const EditSuperAPI = (props) => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [apiInfo, setApiInfo] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    quota: '',
    status: 1,
    expiredAt: '',
    speedLimit: 0,
    resetType: 0
  });
  const [trafficData, setTrafficData] = useState({
    upload: 0,
    download: 0
  });
  
  const api = new ApiClient();
  const serviceId = props.record?.params?.id;

  useEffect(() => {
    if (props.record?.params?.superapiData) {
      const superapiData = props.record.params.superapiData;
      setApiInfo(superapiData);
      
      // 初始化表单数据
      setFormData({
        username: superapiData.username || '',
        password: superapiData.password || '',
        quota: superapiData.quota || '',
        status: superapiData.status || 1,
        expiredAt: superapiData.expiredAt ? new Date(superapiData.expiredAt).toISOString().split('T')[0] : '',
        speedLimit: superapiData.speedLimit || 0,
        resetType: superapiData.type || 0
      });
      setError(null);
    } else {
      setError('No SuperAPI data found in record.');
    }

    setLoading(false);
  }, [props, serviceId]);
  
  // 刷新 SuperAPI 数据的函数
  const refreshSuperAPIData = async () => {
    try {
      setLoading(true);
      
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: serviceId,
        actionName: 'superapi'
      });
      
      if (response && response.data && response.data.record) {
        const superapiData = response.data.record.params?.superapiData;
        if (superapiData) {
          setApiInfo(superapiData);
          
          // 更新表单数据
          setFormData({
            username: superapiData.username || '',
            password: superapiData.password || '',
            quota: superapiData.quota || '',
            status: superapiData.status || 1,
            expiredAt: superapiData.expiredAt ? new Date(superapiData.expiredAt).toISOString().split('T')[0] : '',
            speedLimit: superapiData.speedLimit || 0,
            resetType: superapiData.type || 0
          });
          
          // Clear any existing error messages when successfully refreshing data
          setError(null);
        }
      }
    } catch (error) {
      console.error('Failed to refresh SuperAPI data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTrafficInputChange = (field, value) => {
    setTrafficData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // 调用 AdminJS API 来更新 SuperAPI 数据
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: serviceId,
        actionName: 'updateSuperapi',
        data: formData
      });

      // 检查响应是否成功
      if (response && response.data) {
        // 检查是否有 notice 字段，表示有成功消息
        if (response.data.notice && response.data.notice.message) {
          setSuccess(response.data.notice.message);
        } else {
          setSuccess('SuperAPI information updated successfully!');
        }
        
        // 从响应中更新本地状态，显示最新数据
        if (response.data.record?.params?.superapiData) {
          setApiInfo(response.data.record.params.superapiData);
        } else {
          // 如果响应中没有包含最新数据，主动刷新
          await refreshSuperAPIData();
        }
        
        // 确保没有错误状态
        setError(null);
      } else {
        setError('Failed to update SuperAPI information: Invalid response');
      }
    } catch (err) {
      console.error('Error updating SuperAPI:', err);
      
      // 提取更有用的错误消息
      let errorMessage = 'Network error occurred while updating SuperAPI information.';
      if (err.message) {
        errorMessage = `Error: ${err.message}`;
      }
      
      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: serviceId,
        actionName: 'resetSuperapi'
      });

      // 简化响应处理逻辑：只要请求没有抛出异常，就认为是成功的
      if (response) {
        let successMessage = 'SuperAPI service reset successfully!';
        
        // 优先使用后端返回的消息
        if (response.data?.notice?.message) {
          successMessage = response.data.notice.message;
        }
        
        setSuccess(successMessage);
        setError(null);
        
        // 更新本地状态
        if (response.data?.record?.params?.superapiData) {
          setApiInfo(response.data.record.params.superapiData);
        }
        
        // 主动刷新数据以确保显示最新状态
        setTimeout(() => {
          refreshSuperAPIData();
        }, 500);
      } else {
        setError('未收到服务器响应');
      }
    } catch (err) {
      console.error('Error resetting SuperAPI:', err);
      setError(err.message || '重置 SuperAPI 服务时发生网络错误');
    } finally {
      setSaving(false);
    }
  };

  const handleRedeem = async () => {
    const delta = prompt('Enter the amount to redeem (in bytes):');
    if (!delta || isNaN(Number(delta))) {
      setError('Please enter a valid number for redeem amount.');
      return;
    }

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: serviceId,
        actionName: 'redeemSuperapi',
        data: { delta: parseInt(delta) }
      });

      // 检查响应是否成功
      if (response && response.data) {
        // 检查是否有 notice 字段，表示有成功消息
        if (response.data.notice && response.data.notice.message) {
          setSuccess(response.data.notice.message);
        } else {
          setSuccess(`Successfully redeemed ${delta} bytes from SuperAPI service!`);
        }
        
        // 从响应中更新本地状态，显示最新数据
        if (response.data.record?.params?.superapiData) {
          setApiInfo(response.data.record.params.superapiData);
        } else {
          // 如果响应中没有包含最新数据，主动刷新
          await refreshSuperAPIData();
        }
        
        // 确保没有错误状态
        setError(null);
      } else {
        setError('Failed to redeem from SuperAPI service: Invalid response');
      }
    } catch (err) {
      console.error('Error redeeming SuperAPI:', err);
      
      // 提取更有用的错误消息
      let errorMessage = 'Network error occurred while redeeming from SuperAPI service.';
      if (err.message) {
        errorMessage = `Error: ${err.message}`;
      }
      
      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleUpdateTraffic = async () => {
    if (isNaN(Number(trafficData.upload)) || isNaN(Number(trafficData.download))) {
      setError('Upload and download values must be valid numbers.');
      return;
    }
    
    if (!formData.username || !formData.password) {
      setError('Username and password are required for traffic updates.');
      return;
    }

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: serviceId,
        actionName: 'updateTrafficSuperapi',
        data: {
          username: formData.username,
          password: formData.password,
          upload: Number(trafficData.upload),
          download: Number(trafficData.download)
        }
      });

      // 简化响应处理逻辑：只要请求没有抛出异常，就认为是成功的
      if (response) {
        let successMessage = 'Traffic usage updated successfully!';
        
        // 优先使用后端返回的消息
        if (response.data?.notice?.message) {
          successMessage = response.data.notice.message;
        }
        
        setSuccess(successMessage);
        setError(null);
        
        // 重置流量输入框数据
        setTrafficData({ upload: 0, download: 0 });
        
        // 更新本地状态
        if (response.data?.record?.params?.superapiData) {
          setApiInfo(response.data.record.params.superapiData);
        }
        
        // 主动刷新数据以确保显示最新状态
        setTimeout(() => {
          refreshSuperAPIData();
        }, 500);
      } else {
        setError('未收到服务器响应');
      }
    } catch (err) {
      console.error('Error updating traffic usage:', err);
      
      // 提取更有用的错误消息
      let errorMessage = '更新流量使用量时发生网络错误';
      if (err.response?.data?.message) {
        errorMessage = `错误: ${err.response.data.message}`;
      } else if (err.message) {
        errorMessage = `错误: ${err.message}`;
      }
      
      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (error && !apiInfo) {
    return (
      <Box>
        <Text color="error">{error}</Text>
      </Box>
    );
  }

  const formatBytes = (bytes) => {
    // 处理无效输入
    if (!bytes || bytes === 0 || bytes === '0' || isNaN(Number(bytes))) {
      return '0 B';
    }

    // 确保 bytes 是数字
    const numBytes = Number(bytes);
    if (numBytes <= 0) {
      return '0 B';
    }

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];

    const i = Math.floor(Math.log(numBytes) / Math.log(k));

    // 防止数组越界
    if (i >= sizes.length) {
      return `${(numBytes / Math.pow(k, sizes.length - 1)).toFixed(2)} ${sizes[sizes.length - 1]}`;
    }

    const value = numBytes / Math.pow(k, i);
    return `${value.toFixed(2)} ${sizes[i]}`;
  };

  const getStatusBadge = (status) => {
    if (status === 1) {
      return <Badge variant="success">Active</Badge>;
    } else {
      return <Badge variant="danger">Suspended</Badge>;
    }
  };

  return (
    <Box style={{ 
      background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
      minHeight: '100vh',
      padding: '20px'
    }}>
      {/* 页面标题 */}
      <Box mb="xl" style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '24px',
        borderRadius: '16px',
        color: 'white',
        textAlign: 'center',
        boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)'
      }}>
        <Box display="flex" alignItems="center" justifyContent="center" gap="md" mb="sm">
          <Icon icon="Edit3" style={{ fontSize: '32px', color: 'white' }} />
          <Text variant="h2" style={{ margin: 0, fontWeight: 700, color: 'white' }}>
            Edit SuperAPI Configuration
          </Text>
        </Box>
        <Text variant="lg" style={{ margin: 0, opacity: 0.9, color: 'white' }}>
          Manage and configure your SuperAPI service settings
        </Text>
      </Box>

      {/* 错误和成功消息 */}
      {error && (
        <Box mb="lg" p="md" style={{
          backgroundColor: '#f8d7da',
          borderRadius: '12px',
          border: '1px solid #f5c6cb',
          color: '#721c24'
        }}>
          <Box display="flex" alignItems="center" gap="sm">
            <Icon icon="AlertTriangle" style={{ color: '#721c24' }} />
            <Text style={{ fontWeight: 600 }}>Error</Text>
          </Box>
          <Text mt="xs">{error}</Text>
        </Box>
      )}

      {success && !error && (
        <Box mb="lg" p="md" style={{
          backgroundColor: '#d4edda',
          borderRadius: '12px',
          border: '1px solid #c3e6cb',
          color: '#155724'
        }}>
          <Box display="flex" alignItems="center" gap="sm">
            <Icon icon="CheckCircle" style={{ color: '#155724' }} />
            <Text style={{ fontWeight: 600 }}>Success</Text>
          </Box>
          <Text mt="xs">{success}</Text>
        </Box>
      )}

      {/* 当前服务信息 */}
      <InfoCard title="Current Service Information" variant="primary" icon="Info">
        {/* 服务状态概览 */}
        <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap="lg" mb="xl">
          <Box style={{
            padding: '20px',
            backgroundColor: apiInfo?.status === 1 ? '#d4edda' : '#f8d7da',
            borderRadius: '12px',
            border: `1px solid ${apiInfo?.status === 1 ? '#c3e6cb' : '#f5c6cb'}`,
            textAlign: 'center'
          }}>
            <Icon 
              icon={apiInfo?.status === 1 ? 'CheckCircle' : 'XCircle'} 
              style={{ 
                fontSize: '24px', 
                color: apiInfo?.status === 1 ? '#28a745' : '#dc3545', 
                marginBottom: '8px' 
              }} 
            />
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Service Status
            </Text>
            <Text variant="h4" style={{ 
              fontWeight: 700, 
              color: apiInfo?.status === 1 ? '#28a745' : '#dc3545',
              margin: '4px 0'
            }}>
              {apiInfo?.status === 1 ? 'Active' : 'Suspended'}
            </Text>
          </Box>

          <Box style={{
            padding: '20px',
            backgroundColor: '#e3f2fd',
            borderRadius: '12px',
            border: '1px solid #bbdefb',
            textAlign: 'center'
          }}>
            <Icon icon="Server" style={{ fontSize: '24px', color: '#1976d2', marginBottom: '8px' }} />
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Service ID
            </Text>
            <Text variant="h4" style={{ fontWeight: 700, color: '#1976d2', margin: '4px 0', fontFamily: 'monospace' }}>
              {apiInfo?.id}
            </Text>
          </Box>

          <Box style={{
            padding: '20px',
            backgroundColor: '#f3e5f5',
            borderRadius: '12px',
            border: '1px solid #ce93d8',
            textAlign: 'center'
          }}>
            <Icon icon="User" style={{ fontSize: '24px', color: '#7b1fa2', marginBottom: '8px' }} />
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Client
            </Text>
            <Text variant="h4" style={{ fontWeight: 700, color: '#7b1fa2', margin: '4px 0' }}>
              {apiInfo?.client}
            </Text>
          </Box>
        </Box>

        {/* 详细信息网格 */}
        <Box display="grid" gridTemplateColumns={{ sm: '1fr', md: '1fr 1fr' }} gap="xl">
          <Box>
            <Text variant="sm" style={{ 
              textTransform: 'uppercase', 
              letterSpacing: '0.5px', 
              marginBottom: '16px',
              fontWeight: 600,
              color: '#6c757d'
            }}>
              Service Details
            </Text>
            <Box display="grid" gap="md">
              <Box p="md" style={{ backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                <Text variant="xs" color="grey60">Client ID</Text>
                <Text variant="lg" style={{ fontWeight: 600, fontFamily: 'monospace' }}>
                  {apiInfo?.clientId}
                </Text>
              </Box>
            </Box>
          </Box>

          <Box>
            <Text variant="sm" style={{ 
              textTransform: 'uppercase', 
              letterSpacing: '0.5px', 
              marginBottom: '16px',
              fontWeight: 600,
              color: '#6c757d'
            }}>
              Usage Statistics
            </Text>
            <Box display="grid" gridTemplateColumns="1fr 1fr" gap="md">
              <Box p="md" style={{ backgroundColor: '#e3f2fd', borderRadius: '8px', textAlign: 'center' }}>
                <Text variant="xs" color="grey60">Upload</Text>
                <Text variant="lg" style={{ fontWeight: 600, color: '#1976d2' }}>
                  {formatBytes(apiInfo?.upload)}
                </Text>
              </Box>
              <Box p="md" style={{ backgroundColor: '#e8f5e8', borderRadius: '8px', textAlign: 'center' }}>
                <Text variant="xs" color="grey60">Download</Text>
                <Text variant="lg" style={{ fontWeight: 600, color: '#28a745' }}>
                  {formatBytes(apiInfo?.download)}
                </Text>
              </Box>
            </Box>
          </Box>
        </Box>
      </InfoCard>

      {/* 编辑表单 */}
      <InfoCard title="Edit Configuration" icon="Settings">
        <Box as="form" display="grid" gridTemplateColumns={{ sm: '1fr', md: '1fr 1fr' }} gap="xl">
          <Box>
            <Text variant="sm" style={{ 
              textTransform: 'uppercase', 
              letterSpacing: '0.5px', 
              marginBottom: '16px',
              fontWeight: 600,
              color: '#6c757d'
            }}>
              Authentication Settings
            </Text>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057' }}>Username</Label>
              <Input
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                disabled={saving}
                style={{ 
                  fontFamily: 'monospace',
                  borderRadius: '8px',
                  border: '2px solid #e9ecef',
                  padding: '12px',
                  fontSize: '14px'
                }}
              />
            </FormGroup>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057' }}>Password</Label>
              <Input
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                disabled={saving}
                style={{ 
                  fontFamily: 'monospace',
                  borderRadius: '8px',
                  border: '2px solid #e9ecef',
                  padding: '12px',
                  fontSize: '14px'
                }}
              />
            </FormGroup>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057' }}>Service Status</Label>
              <Select
                value={formData.status}
                onChange={(selected) => handleInputChange('status', selected.value)}
                options={[
                  { value: 0, label: '🔴 Suspended' },
                  { value: 1, label: '🟢 Active' },
                ]}
                isDisabled={saving}
                styles={{
                  control: (provided) => ({
                    ...provided,
                    borderRadius: '8px',
                    border: '2px solid #e9ecef',
                    padding: '4px'
                  })
                }}
              />
            </FormGroup>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057' }}>Reset Type</Label>
              <Select
                value={formData.resetType}
                onChange={(selected) => handleInputChange('resetType', selected.value)}
                options={[
                  { value: 0, label: '🚫 Never Reset' },
                  { value: 1, label: '📅 Monthly Reset' },
                ]}
                isDisabled={saving}
                styles={{
                  control: (provided) => ({
                    ...provided,
                    borderRadius: '8px',
                    border: '2px solid #e9ecef',
                    padding: '4px'
                  })
                }}
              />
            </FormGroup>
          </Box>

          <Box>
            <Text variant="sm" style={{ 
              textTransform: 'uppercase', 
              letterSpacing: '0.5px', 
              marginBottom: '16px',
              fontWeight: 600,
              color: '#6c757d'
            }}>
              Service Configuration
            </Text>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057' }}>Quota Configuration</Label>
              <Box mb="xs">
                <Text variant="xs" style={{ 
                  color: '#6c757d',
                  backgroundColor: '#fff3cd',
                  padding: '8px 12px',
                  borderRadius: '6px',
                  border: '1px solid #ffeaa7',
                  display: 'inline-block'
                }}>
                  💡 JSON format: [&#123;"traffic": bytes, "speed": bytes_per_sec&#125;]
                </Text>
              </Box>
              <Input
                value={formData.quota}
                onChange={(e) => handleInputChange('quota', e.target.value)}
                disabled={saving}
                placeholder='[{"traffic": 107374182400, "speed": 2621440}]'
                style={{ 
                  fontFamily: 'monospace', 
                  fontSize: '12px',
                  borderRadius: '8px',
                  border: '2px solid #e9ecef',
                  padding: '12px',
                  minHeight: '80px'
                }}
              />
            </FormGroup>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057' }}>Expiration Date</Label>
              <Input
                type="date"
                value={formData.expiredAt}
                onChange={(e) => handleInputChange('expiredAt', e.target.value)}
                disabled={saving}
                style={{
                  borderRadius: '8px',
                  border: '2px solid #e9ecef',
                  padding: '12px'
                }}
              />
            </FormGroup>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057' }}>Speed Limit (bytes/s)</Label>
              <Input
                type="number"
                value={formData.speedLimit}
                onChange={(e) => handleInputChange('speedLimit', parseInt(e.target.value) || 0)}
                disabled={saving}
                placeholder="0 for unlimited"
                style={{
                  borderRadius: '8px',
                  border: '2px solid #e9ecef',
                  padding: '12px'
                }}
              />
              <Text variant="xs" style={{ 
                color: '#6c757d',
                marginTop: '8px',
                padding: '8px 12px',
                backgroundColor: '#e3f2fd',
                borderRadius: '6px',
                border: '1px solid #bbdefb'
              }}>
                ⚡ Set to 0 for unlimited speed, or specify bytes per second
              </Text>
            </FormGroup>
          </Box>
        </Box>
      </InfoCard>

      {/* 更新流量使用情况 */}
      <InfoCard title="Update Traffic Usage" icon="Activity" variant="warning">
        <Box mb="lg" p="lg" style={{
          backgroundColor: '#fff3cd',
          borderRadius: '12px',
          border: '1px solid #ffeaa7',
          position: 'relative'
        }}>
          <Box display="flex" alignItems="center" gap="sm" mb="sm">
            <Icon icon="AlertTriangle" style={{ color: '#856404', fontSize: '20px' }} />
            <Text variant="lg" style={{ fontWeight: 600, color: '#856404' }}>
              Important Information
            </Text>
          </Box>
          <Text style={{ color: '#856404', lineHeight: '1.6' }}>
            This feature updates the service's traffic usage by adding <strong>incremental</strong> bytes 
            to the current total. Make sure to enter only the additional usage, not the total amount. 
            Authentication using the SuperAPI credentials is required.
          </Text>
        </Box>
        
        <Box display="grid" gridTemplateColumns={{ sm: '1fr', md: '1fr 1fr' }} gap="xl">
          <Box>
            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Icon icon="Upload" style={{ color: '#007bff' }} />
                Add Upload Usage (bytes)
              </Label>
              <Input
                type="number"
                value={trafficData.upload}
                onChange={(e) => handleTrafficInputChange('upload', e.target.value)}
                disabled={saving}
                style={{ 
                  fontFamily: 'monospace',
                  borderRadius: '8px',
                  border: '2px solid #e9ecef',
                  padding: '12px',
                  fontSize: '14px'
                }}
                placeholder="Enter incremental upload bytes"
              />
              <Box mt="xs" p="xs" style={{
                backgroundColor: '#e3f2fd',
                borderRadius: '6px',
                border: '1px solid #bbdefb'
              }}>
                <Text variant="xs" style={{ color: '#1976d2' }}>
                  📤 Current: {formatBytes(apiInfo?.upload)} | Adding: {formatBytes(trafficData.upload || 0)}
                </Text>
              </Box>
            </FormGroup>
          </Box>

          <Box>
            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Icon icon="Download" style={{ color: '#28a745' }} />
                Add Download Usage (bytes)
              </Label>
              <Input
                type="number"
                value={trafficData.download}
                onChange={(e) => handleTrafficInputChange('download', e.target.value)}
                disabled={saving}
                style={{ 
                  fontFamily: 'monospace',
                  borderRadius: '8px',
                  border: '2px solid #e9ecef',
                  padding: '12px',
                  fontSize: '14px'
                }}
                placeholder="Enter incremental download bytes"
              />
              <Box mt="xs" p="xs" style={{
                backgroundColor: '#e8f5e8',
                borderRadius: '6px',
                border: '1px solid #c3e6cb'
              }}>
                <Text variant="xs" style={{ color: '#28a745' }}>
                  📥 Current: {formatBytes(apiInfo?.download)} | Adding: {formatBytes(trafficData.download || 0)}
                </Text>
              </Box>
            </FormGroup>
          </Box>
        </Box>
        
        <Box mt="xl" display="flex" justifyContent="center">
          <Button
            variant="primary"
            onClick={handleUpdateTraffic}
            disabled={saving}
            style={{
              padding: '12px 32px',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: 600,
              background: 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)',
              border: 'none',
              color: '#212529'
            }}
          >
            {saving ? (
              <Box display="flex" alignItems="center" gap="sm">
                <Icon icon="Loader" style={{ animation: 'spin 1s linear infinite' }} />
                Updating Traffic...
              </Box>
            ) : (
              <Box display="flex" alignItems="center" gap="sm">
                <Icon icon="Activity" />
                Update Traffic Usage
              </Box>
            )}
          </Button>
        </Box>
      </InfoCard>

      {/* 操作按钮 */}
      <InfoCard title="Actions" icon="Tool">
        <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(280px, 1fr))" gap="xl">
          {/* 保存更改 */}
          <Box
            p="xl"
            style={{
              background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
              borderRadius: '16px',
              border: '2px solid #90caf9',
              textAlign: 'center',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)';
              e.currentTarget.style.boxShadow = '0 12px 32px rgba(33, 150, 243, 0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.08)';
            }}
          >
            <Box mb="md">
              <Icon icon="Save" style={{ fontSize: '32px', color: '#1976d2' }} />
            </Box>
            <Text variant="h6" style={{ fontWeight: 700, color: '#1976d2', marginBottom: '8px' }}>
              Save Configuration
            </Text>
            <Text variant="sm" style={{ color: '#424242', marginBottom: '20px', lineHeight: '1.5' }}>
              Apply all configuration changes to the SuperAPI server
            </Text>
            <Button
              variant="primary"
              onClick={handleSave}
              disabled={saving}
              style={{
                width: '100%',
                padding: '12px 24px',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: 600,
                background: 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
                border: 'none'
              }}
            >
              {saving ? (
                <Box display="flex" alignItems="center" justifyContent="center" gap="sm">
                  <Icon icon="Loader" style={{ animation: 'spin 1s linear infinite' }} />
                  Saving Changes...
                </Box>
              ) : (
                <Box display="flex" alignItems="center" justifyContent="center" gap="sm">
                  <Icon icon="Save" />
                  Save Changes
                </Box>
              )}
            </Button>
          </Box>

          {/* 重置服务 */}
          <Box
            p="xl"
            style={{
              background: 'linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%)',
              borderRadius: '16px',
              border: '2px solid #ffb74d',
              textAlign: 'center',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)';
              e.currentTarget.style.boxShadow = '0 12px 32px rgba(255, 152, 0, 0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.08)';
            }}
          >
            <Box mb="md">
              <Icon icon="RotateCcw" style={{ fontSize: '32px', color: '#f57c00' }} />
            </Box>
            <Text variant="h6" style={{ fontWeight: 700, color: '#f57c00', marginBottom: '8px' }}>
              Reset Service
            </Text>
            <Text variant="sm" style={{ color: '#424242', marginBottom: '20px', lineHeight: '1.5' }}>
              Clear usage statistics and reset all counters to zero
            </Text>
            <Button
              onClick={handleReset}
              disabled={saving}
              style={{
                width: '100%',
                padding: '12px 24px',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: 600,
                background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
                border: 'none',
                color: 'white'
              }}
            >
              {saving ? (
                <Box display="flex" alignItems="center" justifyContent="center" gap="sm">
                  <Icon icon="Loader" style={{ animation: 'spin 1s linear infinite' }} />
                  Resetting...
                </Box>
              ) : (
                <Box display="flex" alignItems="center" justifyContent="center" gap="sm">
                  <Icon icon="RotateCcw" />
                  Reset Service
                </Box>
              )}
            </Button>
          </Box>

          {/* 兑换数据 */}
          <Box
            p="xl"
            style={{
              background: 'linear-gradient(135deg, #f3e5f5 0%, #ce93d8 100%)',
              borderRadius: '16px',
              border: '2px solid #ba68c8',
              textAlign: 'center',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)';
              e.currentTarget.style.boxShadow = '0 12px 32px rgba(156, 39, 176, 0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.08)';
            }}
          >
            <Box mb="md">
              <Icon icon="Minus" style={{ fontSize: '32px', color: '#7b1fa2' }} />
            </Box>
            <Text variant="h6" style={{ fontWeight: 700, color: '#7b1fa2', marginBottom: '8px' }}>
              Redeem Data
            </Text>
            <Text variant="sm" style={{ color: '#424242', marginBottom: '20px', lineHeight: '1.5' }}>
              Reduce current usage amount by specified bytes
            </Text>
            <Button
              onClick={handleRedeem}
              disabled={saving}
              style={{
                width: '100%',
                padding: '12px 24px',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: 600,
                background: 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)',
                border: 'none',
                color: 'white'
              }}
            >
              {saving ? (
                <Box display="flex" alignItems="center" justifyContent="center" gap="sm">
                  <Icon icon="Loader" style={{ animation: 'spin 1s linear infinite' }} />
                  Processing...
                </Box>
              ) : (
                <Box display="flex" alignItems="center" justifyContent="center" gap="sm">
                  <Icon icon="Minus" />
                  Redeem Data
                </Box>
              )}
            </Button>
          </Box>
        </Box>

        {/* 操作说明 */}
        <Box mt="xl" p="lg" style={{
          background: 'linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%)',
          borderRadius: '12px',
          border: '1px solid #ffcc02'
        }}>
          <Box display="flex" alignItems="center" gap="sm" mb="md">
            <Icon icon="BookOpen" style={{ color: '#f57c00', fontSize: '20px' }} />
            <Text variant="lg" style={{ fontWeight: 600, color: '#e65100' }}>
              Action Descriptions
            </Text>
          </Box>
          <Box display="grid" gridTemplateColumns={{ sm: '1fr', md: '1fr 1fr 1fr' }} gap="md">
            <Box>
              <Text variant="sm" style={{ fontWeight: 600, color: '#1976d2', marginBottom: '4px' }}>
                💾 Save Changes
              </Text>
              <Text variant="xs" style={{ color: '#424242', lineHeight: '1.4' }}>
                Updates service configuration on SuperAPI server with current form values
              </Text>
            </Box>
            <Box>
              <Text variant="sm" style={{ fontWeight: 600, color: '#f57c00', marginBottom: '4px' }}>
                🔄 Reset Service
              </Text>
              <Text variant="xs" style={{ color: '#424242', lineHeight: '1.4' }}>
                Clears all usage statistics and resets upload/download counters to zero
              </Text>
            </Box>
            <Box>
              <Text variant="sm" style={{ fontWeight: 600, color: '#7b1fa2', marginBottom: '4px' }}>
                ➖ Redeem Data
              </Text>
              <Text variant="xs" style={{ color: '#424242', lineHeight: '1.4' }}>
                Reduces current usage by specified amount (useful for data refunds)
              </Text>
            </Box>
          </Box>
        </Box>
      </InfoCard>
    </Box>
  );
};

export default EditSuperAPI;
