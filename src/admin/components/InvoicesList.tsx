import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import styled from 'styled-components';
import Card from './Card.js';
import CardHeader from './CardHeader.js';
import { Invoice } from '../types/index.js';

const InvoiceItem = styled.a`
  display: block;
  padding: 16px;
  background: #f3f4f6;
  border: 1px solid #9ca3af;
  border-radius: 4px;
  margin-bottom: 12px;
  text-decoration: none;
  color: inherit;

  &:hover {
    background: #e5e7eb;
  }
`;

const StatusBadge = styled.span`
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;

  &.paid {
    background: #22c55e;
    color: white;
  }

  &.unpaid {
    background: #ef4444;
    color: white;
  }

  &.cancelled {
    background: #6b7280;
    color: white;
  }

  &.refunded {
    background: #8b5cf6;
    color: white;
  }

  &.collections {
    background: #dc2626;
    color: white;
  }

  &.payment-pending {
    background: #f59e0b;
    color: white;
  }

  &.overdue {
    background: #b91c1c;
    color: white;
  }

  &.pending {
    background: #f59e0b;
    color: white;
  }
`;

interface InvoicesListProps {
  userInvoices: Invoice[];
  isLoadingInvoices: boolean;
  selectedUser: any;
}

const InvoicesList: React.FC<InvoicesListProps> = ({
  userInvoices,
  isLoadingInvoices,
  selectedUser
}) => {
  const getStatusClass = (invoice: Invoice) => {
    const status = invoice.params?.status;
    const dueDate = invoice.params?.duedate;

    // Check if invoice is overdue (unpaid and past due date)
    if (status === 'Unpaid' && dueDate) {
      const today = new Date();
      const due = new Date(dueDate);
      if (due < today) {
        return 'overdue';
      }
    }

    // Map status to CSS class
    switch (status) {
      case 'Paid':
        return 'paid';
      case 'Unpaid':
        return 'unpaid';
      case 'Cancelled':
        return 'cancelled';
      case 'Refunded':
        return 'refunded';
      case 'Collections':
        return 'collections';
      case 'Payment Pending':
        return 'payment-pending';
      default:
        return 'pending';
    }
  };
  return (
    <Card>
      <CardHeader
        title="Recent Invoices"
        subtitle={userInvoices.length > 0 ? `Showing ${Math.min(userInvoices.length, 5)} of ${userInvoices.length}` : undefined}
      />

      {isLoadingInvoices ? (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">Loading invoices...</Text>
        </Box>
      ) : selectedUser ? (
        userInvoices.length > 0 ? (
          <Box>
            {userInvoices.map(invoice => (
              <InvoiceItem
                key={invoice.id}
                href={`/admin/resources/Invoice/records/${invoice.id}/show`}
              >
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Box display="flex" alignItems="center" gap="default" marginBottom="sm">
                      <Text fontWeight="bold">
                        #{invoice.id}
                      </Text>
                      <Text variant="lg" fontWeight="bold" color="primary">
                        ${invoice.params?.subtotal || '0.00'}
                      </Text>
                    </Box>
                    <Text color="grey60" variant="sm">
                      Due: {invoice.params?.duedate || 'No due date'}
                    </Text>
                  </Box>

                  <StatusBadge className={getStatusClass(invoice)}>
                    {invoice.params?.status || 'PENDING'}
                  </StatusBadge>
                </Box>
              </InvoiceItem>
            ))}
          </Box>
        ) : (
          <Box textAlign="center" padding="xl">
            <Text color="grey60">No invoices found</Text>
          </Box>
        )
      ) : (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">Select a user to see their invoices</Text>
        </Box>
      )}
    </Card>
  );
};

export default InvoicesList;
