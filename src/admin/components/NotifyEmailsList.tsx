import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import Card from './Card.js';
import { NotifyEmail } from '../types/index.js';

interface NotifyEmailsListProps {
  userEmails: NotifyEmail[];
  isLoadingEmails: boolean;
  selectedUser: any;
}

const NotifyEmailsList: React.FC<NotifyEmailsListProps> = ({
  userEmails,
  isLoadingEmails,
  selectedUser
}) => {
  return (
    <Card>
      <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="lg">
        <Text variant="lg" fontWeight="bold">
          Recent Notify Emails
        </Text>
        {userEmails.length > 0 && (
          <Text variant="sm" color="grey">
            Last {userEmails.length} emails
          </Text>
        )}
      </Box>

      {isLoadingEmails ? (
        <Text>Loading emails...</Text>
      ) : selectedUser ? (
        userEmails.length > 0 ? (
          <Box>
            {userEmails.map((email, index) => (
              <Box
                key={email.id}
                marginBottom="default"
                padding="lg"
                backgroundColor={index % 2 === 0 ? 'grey20' : 'white'}
                borderRadius="default"
              >
                <Box display="flex" justifyContent="space-between" marginBottom="sm">
                  <Text fontWeight="bold">
                    {email.params.subject}
                  </Text>
                  <Text color="grey80">
                    {new Date(email.params.createdAt).toLocaleString()}
                  </Text>
                </Box>
                
                <Box 
                  backgroundColor="white" 
                  padding="lg" 
                  borderRadius="default"
                  marginTop="sm"
                  style={{ 
                    maxHeight: '100px', 
                    overflow: 'auto',
                    border: '1px solid #eee' 
                  }}
                >
                  <Text variant="sm" style={{ whiteSpace: 'pre-wrap' }}>
                    {email.params.templateParam}
                  </Text>
                </Box>

                <Box display="flex" gap="default" marginTop="sm">
                  {email.params.error && (
                    <Text variant="sm" color="error">
                      Error: {email.params.error}
                    </Text>
                  )}
                </Box>
              </Box>
            ))}
          </Box>
        ) : (
          <Text color="grey">No notify emails found for this user</Text>
        )
      ) : (
        <Text color="grey">Select a user to see their notify emails</Text>
      )}
    </Card>
  );
};

export default NotifyEmailsList;
