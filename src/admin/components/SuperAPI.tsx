import React, { useEffect, useState } from 'react';
import {
  Box,
  Text,
  Loader,
  Badge,
  Button,
  Icon
} from '@adminjs/design-system';

const InfoCard = ({ children, title, variant = 'default', icon }) => {
  const getCardStyle = () => {
    const baseStyle = {
      background: 'white',
      border: '1px solid #e9ecef',
      borderRadius: '12px',
      padding: '0',
      marginBottom: '20px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
      transition: 'all 0.3s ease'
    };

    if (variant === 'primary') {
      return {
        ...baseStyle,
        border: '1px solid #007bff',
        boxShadow: '0 4px 20px rgba(0,123,255,0.15)'
      };
    }

    if (variant === 'success') {
      return {
        ...baseStyle,
        border: '1px solid #28a745',
        boxShadow: '0 4px 20px rgba(40,167,69,0.15)'
      };
    }

    if (variant === 'warning') {
      return {
        ...baseStyle,
        border: '1px solid #ffc107',
        boxShadow: '0 4px 20px rgba(255,193,7,0.15)'
      };
    }

    return baseStyle;
  };

  const getHeaderStyle = () => {
    const baseStyle = {
      padding: '20px 24px',
      borderBottom: '1px solid #e9ecef',
      borderRadius: '12px 12px 0 0',
      display: 'flex',
      alignItems: 'center',
      gap: '12px'
    };

    if (variant === 'primary') {
      return {
        ...baseStyle,
        background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
        color: 'white',
        borderBottom: '1px solid #007bff'
      };
    }

    if (variant === 'success') {
      return {
        ...baseStyle,
        background: 'linear-gradient(135deg, #28a745 0%, #1e7e34 100%)',
        color: 'white',
        borderBottom: '1px solid #28a745'
      };
    }

    if (variant === 'warning') {
      return {
        ...baseStyle,
        background: 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)',
        color: '#212529',
        borderBottom: '1px solid #ffc107'
      };
    }

    return {
      ...baseStyle,
      background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)'
    };
  };

  return (
    <Box style={getCardStyle()}>
      {title && (
        <Box style={getHeaderStyle()}>
          {icon && (
            <Icon 
              icon={icon} 
              style={{ 
                fontSize: '20px',
                color: variant === 'warning' ? '#212529' : (variant === 'primary' || variant === 'success') ? 'white' : '#495057'
              }} 
            />
          )}
          <Text variant="h6" style={{ 
            margin: 0, 
            fontWeight: 600, 
            color: variant === 'warning' ? '#212529' : (variant === 'primary' || variant === 'success') ? 'white' : '#495057'
          }}>
            {title}
          </Text>
        </Box>
      )}
      <Box style={{ padding: '24px' }}>
        {children}
      </Box>
    </Box>
  );
};

const formatBytes = (bytes) => {
  // 处理无效输入
  if (!bytes || bytes === 0 || bytes === '0' || isNaN(Number(bytes))) {
    return '0 B';
  }

  // 确保 bytes 是数字
  const numBytes = Number(bytes);
  if (numBytes <= 0) {
    return '0 B';
  }

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(numBytes) / Math.log(k));

  // 防止数组越界
  if (i >= sizes.length) {
    return `${(numBytes / Math.pow(k, sizes.length - 1)).toFixed(2)} ${sizes[sizes.length - 1]}`;
  }

  const value = numBytes / Math.pow(k, i);
  
  // 格式化数字显示
  if (value >= 100) {
    return `${value.toFixed(0)} ${sizes[i]}`;
  } else if (value >= 10) {
    return `${value.toFixed(1)} ${sizes[i]}`;
  } else {
    return `${value.toFixed(2)} ${sizes[i]}`;
  }
};

const formatDate = (dateString) => {
  // 处理无效输入
  if (!dateString ||
      dateString === 'Invalid Date' ||
      dateString.toString().includes('1969') ||
      dateString.toString().includes('1899') ||
      dateString.toString().includes('NaN')) {
    return 'Not set';
  }

  try {
    const date = new Date(dateString);
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return 'Not set';
    }

    // 检查是否是默认的无效日期
    if (date.getFullYear() < 1970 || date.getFullYear() > 2100) {
      return 'Not set';
    }

    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    let formattedDate = date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
    
    // 如果是过期时间，添加状态提示
    if (diffDays < 0) {
      formattedDate += ' (已过期)';
    } else if (diffDays <= 7) {
      formattedDate += ` (${diffDays}天后过期)`;
    }
    
    return formattedDate;
  } catch {
    return 'Not set';
  }
};

const getStatusBadge = (status) => {
  if (status === 1) {
    return <Badge variant="success" style={{ fontSize: '12px', padding: '4px 8px' }}>Active</Badge>;
  } else {
    return <Badge variant="danger" style={{ fontSize: '12px', padding: '4px 8px' }}>Suspended</Badge>;
  }
};

const getResetTypeBadge = (type) => {
  if (type === 1) {
    return <Badge variant="primary" style={{ fontSize: '12px', padding: '4px 8px' }}>Monthly Reset</Badge>;
  } else {
    return <Badge variant="secondary" style={{ fontSize: '12px', padding: '4px 8px' }}>No Reset</Badge>;
  }
};

// 添加进度条组件
const ProgressBar = ({ current, max, label, color = '#007bff' }) => {
  const percentage = max > 0 ? Math.min((current / max) * 100, 100) : 0;
  
  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb="xs">
        <Text variant="sm" color="grey60">{label}</Text>
        <Text variant="sm" style={{ fontWeight: 600 }}>
          {formatBytes(current)} / {formatBytes(max)}
        </Text>
      </Box>
      <Box style={{
        width: '100%',
        height: '8px',
        backgroundColor: '#e9ecef',
        borderRadius: '4px',
        overflow: 'hidden'
      }}>
        <Box style={{
          width: `${percentage}%`,
          height: '100%',
          backgroundColor: percentage > 90 ? '#dc3545' : percentage > 75 ? '#ffc107' : color,
          transition: 'all 0.3s ease'
        }} />
      </Box>
      <Text variant="xs" color="grey60" mt="xs" style={{ textAlign: 'center' }}>
        {percentage.toFixed(1)}% 已使用
      </Text>
    </Box>
  );
};

// 添加统计卡片组件
const StatCard = ({ icon, title, value, subtitle, color = '#007bff', trend }) => (
  <Box
    p="lg"
    style={{
      backgroundColor: '#f8f9fa',
      borderRadius: '12px',
      border: '1px solid #e9ecef',
      textAlign: 'center',
      position: 'relative',
      transition: 'transform 0.2s ease',
      cursor: 'default'
    }}
    onMouseEnter={(e) => {
      e.currentTarget.style.transform = 'translateY(-2px)';
      e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';
    }}
    onMouseLeave={(e) => {
      e.currentTarget.style.transform = 'translateY(0)';
      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.08)';
    }}
  >
    <Icon icon={icon} style={{ fontSize: '24px', color, marginBottom: '8px' }} />
    <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
      {title}
    </Text>
    <Text variant="h4" style={{ fontWeight: 700, color: '#212529', marginBottom: '4px' }}>
      {value}
    </Text>
    {subtitle && (
      <Text variant="xs" color="grey60">
        {subtitle}
      </Text>
    )}
    {trend && (
      <Box 
        style={{ 
          position: 'absolute', 
          top: '12px', 
          right: '12px',
          fontSize: '12px',
          color: trend > 0 ? '#28a745' : trend < 0 ? '#dc3545' : '#6c757d'
        }}
      >
        <Icon icon={trend > 0 ? 'TrendingUp' : trend < 0 ? 'TrendingDown' : 'Minus'} />
      </Box>
    )}
  </Box>
);

const SuperAPI = (props) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [apiInfo, setApiInfo] = useState(null);

  // 检查是否是 ActionProps（来自 AdminJS action）
  const isActionProps = 'record' in props;
  const serviceId = isActionProps ? props.record?.params?.id : props.serviceId;

  useEffect(() => {
    const fetchSuperAPIData = async () => {
      if (!serviceId) {
        setError('No service ID provided');
        setLoading(false);
        return;
      }
        
      try {
        setLoading(true);
        setError(null);
        
        // 调用我们的 SuperAPI 服务
        const response = await fetch('/admin/api/resources/Service/actions/superapi', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            recordIds: [serviceId],
            resourceId: 'Service',
            serviceId: serviceId
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.record?.params?.superapiData) {
          setApiInfo(data.record.params.superapiData);
          setError(null);
        } else {
          setError('No SuperAPI data available for this service');
        }
      } catch (err) {
        console.error('Error fetching SuperAPI data:', err);
        setError(`Failed to load SuperAPI data: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    if (isActionProps) {
      // Try to get superapiData from the record
      const superapiData = props.record?.params?.superapiData;
      
      if (superapiData) {
        setApiInfo(superapiData);
        setError(null);
        setLoading(false);
      } else {
        console.log('No superapiData in record, fetching from API');
        fetchSuperAPIData();
      }
    } else {
      // Direct component usage - fetch data
      fetchSuperAPIData();
    }
  }, [serviceId, isActionProps, props.record]);

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <Box>
        <Text color="error">{error}</Text>
      </Box>
    );
  }

  if (!apiInfo) {
    return (
      <Box>
        <Text>No SuperAPI information available</Text>
      </Box>
    );
  }

  return (
    <Box style={{ 
      background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
      minHeight: '100vh',
      padding: '20px'
    }}>
      {/* 页面标题 */}
      <Box mb="xl" style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '24px',
        borderRadius: '16px',
        color: 'white',
        textAlign: 'center',
        boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)'
      }}>
        <Box display="flex" alignItems="center" justifyContent="center" gap="md" mb="sm">
          <Icon icon="Server" style={{ fontSize: '32px', color: 'white' }} />
          <Text variant="h2" style={{ margin: 0, fontWeight: 700, color: 'white' }}>
            SuperAPI Service Dashboard
          </Text>
        </Box>
        <Text variant="lg" style={{ margin: 0, opacity: 0.9, color: 'white' }}>
          Service ID: {apiInfo.id} | Client: {apiInfo.client}
        </Text>
      </Box>

      {/* 状态概览卡片 */}
      <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap="lg" mb="xl">
        <StatCard
          icon="Activity"
          title="Service Status"
          value={apiInfo.status === 1 ? 'Active' : 'Suspended'}
          subtitle=""
          color={apiInfo.status === 1 ? '#28a745' : '#dc3545'}
          trend={null}
        />
        <StatCard
          icon="RotateCw"
          title="Reset Type"
          value={apiInfo.type === 1 ? 'Monthly' : 'Never'}
          subtitle=""
          color={apiInfo.type === 1 ? '#007bff' : '#6c757d'}
          trend={null}
        />
        <StatCard
          icon="Zap"
          title="Speed Limit"
          value={apiInfo.speedLimit ? formatBytes(apiInfo.speedLimit) + '/s' : 'Unlimited'}
          subtitle=""
          color="#ffc107"
          trend={null}
        />
        <StatCard
          icon="Clock"
          title="Expires"
          value={new Date(apiInfo.expiredAt) < new Date() ? 'Expired' : 'Active'}
          subtitle={formatDate(apiInfo.expiredAt).includes('过期') ? 'Renewal Required' : 'Valid'}
          color={new Date(apiInfo.expiredAt) < new Date() ? '#dc3545' : '#28a745'}
          trend={null}
        />
      </Box>

      {/* 基本信息卡片 */}
      <InfoCard title="Service Information" variant="primary" icon="Info">
        <Box display="grid" gridTemplateColumns={{ xs: '1fr', md: '1fr 1fr' }} gap="xl">
          <Box>
            <Box mb="lg">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '8px' }}>
                Service Details
              </Text>
              <Box display="grid" gridTemplateColumns="1fr 1fr" gap="md">
                <Box>
                  <Text variant="xs" color="grey60">Service ID</Text>
                  <Text variant="lg" style={{ fontWeight: 600, fontFamily: 'monospace' }}>
                    {apiInfo.id}
                  </Text>
                </Box>
                <Box>
                  <Text variant="xs" color="grey60">Client ID</Text>
                  <Text variant="lg" style={{ fontWeight: 600, fontFamily: 'monospace' }}>
                    {apiInfo.clientId}
                  </Text>
                </Box>
              </Box>
            </Box>

            <Box mb="lg">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '8px' }}>
                Authentication
              </Text>
              <Box display="grid" gridTemplateColumns="1fr 1fr" gap="md">
                <Box>
                  <Text variant="xs" color="grey60">Username</Text>
                  <Text variant="lg" style={{ fontWeight: 600, fontFamily: 'monospace' }}>
                    {apiInfo.username}
                  </Text>
                </Box>
                <Box>
                  <Text variant="xs" color="grey60">Password</Text>
                  <Text variant="lg" style={{ fontWeight: 600, fontFamily: 'monospace' }}>
                    {apiInfo.password}
                  </Text>
                </Box>
              </Box>
            </Box>
          </Box>

          <Box>
            <Box mb="lg">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '8px' }}>
                Status & Configuration
              </Text>
              <Box display="grid" gridTemplateColumns="1fr 1fr" gap="md">
                <Box>
                  <Text variant="xs" color="grey60">Status</Text>
                  <Box mt="xs">
                    {getStatusBadge(apiInfo.status)}
                  </Box>
                </Box>
                <Box>
                  <Text variant="xs" color="grey60">Reset Type</Text>
                  <Box mt="xs">
                    {getResetTypeBadge(apiInfo.type)}
                  </Box>
                </Box>
              </Box>
            </Box>

            <Box mb="lg">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '8px' }}>
                Quota Configuration
              </Text>
              <Box p="md" style={{
                backgroundColor: '#f8f9fa',
                borderRadius: '8px',
                border: '1px solid #e9ecef'
              }}>
                <Text variant="sm" style={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                  {apiInfo.quota || 'Not configured'}
                </Text>
              </Box>
            </Box>
          </Box>
        </Box>
      </InfoCard>

      {/* 使用量统计卡片 */}
      <InfoCard title="Usage Statistics" variant="success" icon="BarChart3">
        {/* 尝试解析 quota 来显示进度条 */}
        {(() => {
          try {
            const quotaConfig = JSON.parse(apiInfo.quota || '[]');
            const trafficQuota = quotaConfig[0]?.traffic;
            
            if (trafficQuota) {
              const totalUsage = (Number(apiInfo.upload) || 0) + (Number(apiInfo.download) || 0);
              return (
                <Box mb="xl">
                  <ProgressBar
                    current={totalUsage}
                    max={trafficQuota}
                    label="Total Traffic Usage"
                    color="#28a745"
                  />
                </Box>
              );
            }
          } catch (e) {
            // 忽略解析错误
          }
          return null;
        })()}

        <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(250px, 1fr))" gap="lg">
          <StatCard
            icon="Upload"
            title="Upload Usage"
            value={formatBytes(apiInfo.upload)}
            subtitle=""
            color="#007bff"
            trend={null}
          />
          <StatCard
            icon="Download"
            title="Download Usage"
            value={formatBytes(apiInfo.download)}
            subtitle=""
            color="#28a745"
            trend={null}
          />
          <StatCard
            icon="HardDrive"
            title="Total Usage"
            value={formatBytes((Number(apiInfo.upload) || 0) + (Number(apiInfo.download) || 0))}
            subtitle=""
            color="#6f42c1"
            trend={null}
          />
        </Box>
      </InfoCard>

      {/* 时间信息卡片 */}
      <InfoCard title="Time Information" variant="warning" icon="Calendar">
        <Box display="grid" gridTemplateColumns={{ xs: '1fr', md: 'repeat(3, 1fr)' }} gap="lg">
          <Box style={{
            padding: '20px',
            backgroundColor: '#fff',
            borderRadius: '12px',
            border: '1px solid #e9ecef',
            textAlign: 'center'
          }}>
            <Icon icon="RotateCcw" style={{ fontSize: '24px', color: '#007bff', marginBottom: '8px' }} />
            <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
              Last Reset
            </Text>
            <Text variant="lg" style={{ fontWeight: 600 }}>
              {formatDate(apiInfo.resetDate)}
            </Text>
          </Box>

          <Box style={{
            padding: '20px',
            backgroundColor: '#fff',
            borderRadius: '12px',
            border: '1px solid #e9ecef',
            textAlign: 'center'
          }}>
            <Icon icon="RefreshCw" style={{ fontSize: '24px', color: '#28a745', marginBottom: '8px' }} />
            <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
              Last Updated
            </Text>
            <Text variant="lg" style={{ fontWeight: 600 }}>
              {formatDate(apiInfo.updatedAt)}
            </Text>
          </Box>

          <Box style={{
            padding: '20px',
            backgroundColor: new Date(apiInfo.expiredAt) < new Date() ? '#fff5f5' : '#f0fff4',
            borderRadius: '12px',
            border: `1px solid ${new Date(apiInfo.expiredAt) < new Date() ? '#fed7d7' : '#c6f6d5'}`,
            textAlign: 'center'
          }}>
            <Icon 
              icon="AlertTriangle" 
              style={{ 
                fontSize: '24px', 
                color: new Date(apiInfo.expiredAt) < new Date() ? '#dc3545' : '#28a745', 
                marginBottom: '8px' 
              }} 
            />
            <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
              Expiration
            </Text>
            <Text variant="lg" style={{ 
              fontWeight: 600, 
              color: new Date(apiInfo.expiredAt) < new Date() ? '#dc3545' : '#28a745' 
            }}>
              {formatDate(apiInfo.expiredAt)}
            </Text>
          </Box>
        </Box>
      </InfoCard>
    </Box>
  );
};

export default SuperAPI;
