import React from 'react';
import { Text } from '@adminjs/design-system';
import Card from './Card.js';
import SuperAPI from './SuperAPI.js';

interface Service {
  id?: string;
  params?: {
    id?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

interface SuperAPIInfoProps {
  currentService: Service | null;
  superApiKey: number;
}

const SuperAPIInfo: React.FC<SuperAPIInfoProps> = ({ currentService, superApiKey }) => {
  // Check if currentService exists and has an id property (either directly or in params)
  const serviceId = currentService?.id || currentService?.params?.id;
  const hasValidServiceId = serviceId && serviceId !== undefined;



  return (
    <Card>
      <Text variant="lg" fontWeight="bold" marginBottom="default">
        SuperAPI Info
      </Text>
      {hasValidServiceId ? (
        <SuperAPI key={superApiKey} serviceId={serviceId} />
      ) : (
        <Text color="grey">Select a service to view SuperAPI info</Text>
      )}
    </Card>
  );
};

export default SuperAPIInfo;
