import React, { useState } from 'react';
import { Box, Button, Text, Input, MessageBox } from '@adminjs/design-system';
import { ApiClient } from 'adminjs';

const UpdatePassword: React.FC<any> = ({ record, resource, userId }) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const api = new ApiClient();

  const actualUserId = userId || record?.params?.id || record?.id;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset states
    setError(null);
    setSuccess(false);

    // Validate passwords
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    setLoading(true);
    try {
      await api.recordAction({
        resourceId: 'User',
        recordId: actualUserId,
        actionName: 'update-password',
        data: {
          password,
        },
      });

      setSuccess(true);
      setPassword('');
      setConfirmPassword('');
    } catch (err: any) {
      setError(err.message || 'Failed to update password');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      bg="white"
      p="lg"
      borderRadius="lg"
      boxShadow="lg"
      maxWidth="500px"
      mx="auto"
    >
      <Text variant="lg" fontWeight="bold" marginBottom="md">
        Update Password
      </Text>
      
      {/* User Info Card */}
      <Box 
        bg="grey20" 
        p="md" 
        borderRadius="md" 
        marginBottom="lg"
        border="1px solid"
        borderColor="grey40"
      >
        <Text fontSize="sm" color="grey60" marginBottom="xs">User Information</Text>
        <Text fontWeight="bold">ID: {actualUserId}</Text>
        {record?.params?.email && (
          <Text color="grey80">Email: {record.params.email}</Text>
        )}
      </Box>

      <form onSubmit={handleSubmit}>
        <Box marginBottom="lg">
          <Text marginBottom="sm">New Password</Text>
          <Input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter new password (minimum 8 characters)"
            width={1}
            required
          />
        </Box>

        <Box marginBottom="lg">
          <Text marginBottom="sm">Confirm Password</Text>
          <Input
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Confirm new password"
            width={1}
            required
          />
        </Box>

        {error && (
          <MessageBox
            message={error}
            variant="danger"
            marginBottom="lg"
          />
        )}

        {success && (
          <MessageBox
            message="Password updated successfully!"
            variant="success"
            marginBottom="lg"
          />
        )}

        <Box display="flex" style={{ gap: '8px' }}>
          <Button
            variant="primary"
            type="submit"
            disabled={loading || !password || !confirmPassword}
          >
            {loading ? 'Updating...' : 'Update Password'}
          </Button>
          
          <Button
            variant="light"
            type="button"
            onClick={() => {
              setPassword('');
              setConfirmPassword('');
              setError(null);
              setSuccess(false);
            }}
          >
            Clear
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default UpdatePassword;