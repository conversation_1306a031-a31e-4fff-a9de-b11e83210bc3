import React, { useEffect, useState } from 'react';
import { ApiClient } from 'adminjs';
import { Box, Table, TableHead, TableBody, TableRow, TableCell, Link, Badge, Text, Button } from '@adminjs/design-system';

interface UserInvoicesProps {
  record?: any;
  property?: any;
}

interface Invoice {
  id: number;
  invoicenum: string;
  date: string;
  duedate: string;
  datepaid: string;
  status: string;
  total: number;
  paymentmethod: string;
}

const UserInvoices: React.FC<UserInvoicesProps> = ({ record }) => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const pageSize = 10;
  const [total, setTotal] = useState(0);
  const api = new ApiClient();
  
  const userId = record?.params?.id;

  useEffect(() => {
    const fetchUserInvoices = async () => {
      if (!userId) {
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // 获取用户的账单，带分页参数
        const response = await api.resourceAction({
          resourceId: 'Invoice',
          actionName: 'list',
          params: {
            filters: {
              userid: userId
            },
            page,
            perPage: pageSize
          }
        });
        
        if (response.data?.records) {
          setInvoices(response.data.records.map((record: any) => record.params));
          setTotal(response.data.meta?.total || 0);
        }
      } catch (error) {
        console.error('Failed to fetch user invoices:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserInvoices();
  }, [userId, page]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Paid': return 'success';
      case 'Unpaid': return 'danger';
      case 'Cancelled': return 'secondary';
      case 'Refunded': return 'warning';
      case 'Collections': return 'danger';
      case 'Payment Pending': return 'warning';
      default: return 'primary';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString || dateString.includes('NaN') || dateString === '0000-00-00') return '-';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return '-';
    }
  };

  const formatAmount = (amount: number) => {
    if (!amount) return '-';
    return `$${Number(amount).toFixed(2)}`;
  };

  const isOverdue = (duedate: string, status: string) => {
    if (status === 'Paid' || !duedate) return false;
    try {
      const due = new Date(duedate);
      const now = new Date();
      return due < now;
    } catch {
      return false;
    }
  };

  if (loading) {
    return <Text>Loading invoices...</Text>;
  }

  if (invoices.length === 0) {
    return <Text>No invoices found for this user.</Text>;
  }

  // 账单按id倒序（后端已分页，这里只需排序当前页）
  const sortedInvoices = [...invoices].sort((a, b) => b.id - a.id);
  const totalPages = Math.ceil(total / pageSize);

  // 计算统计信息
  const totalAmount = invoices.reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const paidAmount = invoices
    .filter(invoice => invoice.status === 'Paid')
    .reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const unpaidAmount = invoices
    .filter(invoice => invoice.status === 'Unpaid')
    .reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const overdueCount = invoices.filter(invoice => 
    isOverdue(invoice.duedate, invoice.status)
  ).length;

  return (
    <Box>
      <Text variant="h4" mb="lg">User Invoices ({invoices.length})</Text>
      
      {/* 统计信息 */}
      <Box
        mb="lg"
        p="xl"
        style={{
          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
          borderRadius: '8px',
          border: '1px solid #dee2e6',
          boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
        }}
      >
        <Text variant="h6" mb="lg" color="grey80" style={{ fontWeight: 600 }}>
          Invoice Summary
        </Text>
        <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap="lg">
          <Box
            p="md"
            style={{
              backgroundColor: 'white',
              borderRadius: '6px',
              border: '1px solid #e9ecef',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Total Amount
            </Text>
            <Text variant="h5" mt="xs" style={{ fontWeight: 700, color: '#495057' }}>
              {formatAmount(totalAmount)}
            </Text>
          </Box>
          <Box
            p="md"
            style={{
              backgroundColor: 'white',
              borderRadius: '6px',
              border: '1px solid #d4edda',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Paid Amount
            </Text>
            <Text variant="h5" mt="xs" color="success" style={{ fontWeight: 700 }}>
              {formatAmount(paidAmount)}
            </Text>
          </Box>
          <Box
            p="md"
            style={{
              backgroundColor: 'white',
              borderRadius: '6px',
              border: '1px solid #f8d7da',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Unpaid Amount
            </Text>
            <Text variant="h5" mt="xs" color="danger" style={{ fontWeight: 700 }}>
              {formatAmount(unpaidAmount)}
            </Text>
          </Box>
          <Box
            p="md"
            style={{
              backgroundColor: 'white',
              borderRadius: '6px',
              border: '1px solid #f8d7da',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Overdue Invoices
            </Text>
            <Text variant="h5" mt="xs" color="danger" style={{ fontWeight: 700 }}>
              {overdueCount}
            </Text>
          </Box>
        </Box>
      </Box>

      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Invoice #</TableCell>
            <TableCell>Date</TableCell>
            <TableCell>Due Date</TableCell>
            <TableCell>Date Paid</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Total</TableCell>
            <TableCell>Payment Method</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {sortedInvoices.map((invoice) => (
            <TableRow key={invoice.id}>
              <TableCell>{invoice.invoicenum || invoice.id}</TableCell>
              <TableCell>{formatDate(invoice.date)}</TableCell>
              <TableCell>
                <Text color={isOverdue(invoice.duedate, invoice.status) ? 'danger' : 'default'}>
                  {formatDate(invoice.duedate)}
                </Text>
              </TableCell>
              <TableCell>{formatDate(invoice.datepaid)}</TableCell>
              <TableCell>
                <Badge variant={getStatusColor(invoice.status)}>
                  {invoice.status}
                </Badge>
              </TableCell>
              <TableCell>{formatAmount(invoice.total)}</TableCell>
              <TableCell>{invoice.paymentmethod || '-'}</TableCell>
              <TableCell>
                <Link href={`/admin/resources/Invoice/records/${invoice.id}/show`}>
                  View Details
                </Link>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* 分页按钮 - adminjs 风格美化，支持页码跳转和省略号 */}
      {totalPages > 1 && (
        <Box mt="xl" display="flex" justifyContent="center" alignItems="center" gap="md">
          <Button variant="primary" size="sm" disabled={page === 1} onClick={() => setPage(page - 1)}>
            上一页
          </Button>
          {/* 页码按钮区块 */}
          <Box display="flex" alignItems="center" gap="sm">
            {(() => {
              const pageButtons = [];
              const maxPageButtons = 5; // 最多显示5个页码按钮
              let start = Math.max(1, page - 2);
              let end = Math.min(totalPages, page + 2);
              if (page <= 3) {
                end = Math.min(totalPages, maxPageButtons);
              } else if (page >= totalPages - 2) {
                start = Math.max(1, totalPages - maxPageButtons + 1);
              }
              // 首页省略号
              if (start > 1) {
                pageButtons.push(
                  <Button key={1} size="sm" variant={page === 1 ? 'contained' : 'text'} onClick={() => setPage(1)}>
                    1
                  </Button>
                );
                if (start > 2) {
                  pageButtons.push(<Text key="start-ellipsis">...</Text>);
                }
              }
              // 中间页码
              for (let i = start; i <= end; i++) {
                pageButtons.push(
                  <Button
                    key={i}
                    size="sm"
                    variant={page === i ? 'contained' : 'text'}
                    onClick={() => setPage(i)}
                    style={page === i ? { fontWeight: 700 } : {}}
                  >
                    {i}
                  </Button>
                );
              }
              // 末尾省略号
              if (end < totalPages) {
                if (end < totalPages - 1) {
                  pageButtons.push(<Text key="end-ellipsis">...</Text>);
                }
                pageButtons.push(
                  <Button key={totalPages} size="sm" variant={page === totalPages ? 'contained' : 'text'} onClick={() => setPage(totalPages)}>
                    {totalPages}
                  </Button>
                );
              }
              return pageButtons;
            })()}
          </Box>
          <Button variant="primary" size="sm" disabled={page === totalPages} onClick={() => setPage(page + 1)}>
            下一页
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default UserInvoices;
