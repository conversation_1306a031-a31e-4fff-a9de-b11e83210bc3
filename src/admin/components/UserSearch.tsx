import React, { useState, useRef } from 'react';
import { Box, Text, Input, Button } from '@adminjs/design-system';
import { ApiClient } from 'adminjs';
import styled from 'styled-components';
import { User } from '../types/index.js';

const SearchContainer = styled(Box)`
  background: white;
  padding: 24px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
  position: relative;
  width: 100%;
`;

const SearchResultsContainer = styled(Box)`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 8px;
`;

const SearchResultItem = styled(Box)`
  padding: 16px 24px;
  cursor: pointer;
  border-bottom: 1px solid #e5e7eb;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #f3f4f6;
  }
`;

interface UserSearchProps {
  onUserSelect: (user: User) => void;
}

const UserSearch: React.FC<UserSearchProps> = ({ onUserSelect }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const api = new ApiClient();
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);

  const handleSearch = async (value: string) => {
    setSearchTerm(value);

    // Clear previous timeout
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    // Don't search if input is empty
    if (!value.trim()) {
      setSearchResults([]);
      return;
    }

    // Debounce search requests
    searchTimeout.current = setTimeout(async () => {
      setIsLoading(true);
      try {
        const response = await api.searchRecords({
          resourceId: 'User',
          query: value,
          searchProperty: 'email',
        });
        setSearchResults(response);
      } catch (error) {
        console.error('Search failed:', error);
      } finally {
        setIsLoading(false);
      }
    }, 300);
  };

  const handleResultClick = (user: User) => {
    onUserSelect(user);
    setSearchResults([]);
    setSearchTerm('');
  };

  const handleConfirmSearch = () => {
    if (searchTerm.trim()) {
      handleSearch(searchTerm);
    }
  };

  return (
    <SearchContainer>
      <Box display="flex" alignItems="center" justifyContent="center">
        <Input
          style={{ width: '30%' }}
          placeholder="Search users by email..."
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          onKeyPress={e => {
            if (e.key === 'Enter') {
              handleConfirmSearch();
            }
          }}
        />
        <Button onClick={handleConfirmSearch} variant="primary" style={{ marginLeft: '10px', minWidth: '100px' }}>
          Search
        </Button>
      </Box>

      {searchResults.length > 0 && (
        <SearchResultsContainer>
          {searchResults.map(user => (
            <SearchResultItem key={user.id} onClick={() => handleResultClick(user)}>
              <Text>
                {user.params?.email || 'No email'}
              </Text>
            </SearchResultItem>
          ))}
        </SearchResultsContainer>
      )}

      {isLoading && (
        <Box marginTop="sm" textAlign="center">
          <Text color="grey60">Searching...</Text>
        </Box>
      )}
    </SearchContainer>
  );
};

export default UserSearch;
