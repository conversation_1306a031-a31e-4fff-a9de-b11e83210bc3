import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import styled from 'styled-components';
import Card from './Card.js';
import CardHeader from './CardHeader.js';
import { Wallet } from '../types/index.js';

const BalanceCard = styled(Box)`
  background: #0ea5e9;
  padding: 32px;
  border-radius: 4px;
  text-align: center;
  color: white;
`;

const WalletGrid = styled(Box)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const InfoItem = styled(Box)`
  padding: 16px;
  background: #f3f4f6;
  border-radius: 4px;
  border: 1px solid #9ca3af;
`;

interface WalletInfoProps {
  userWallet: Wallet | null;
  isLoadingWallet: boolean;
  selectedUser: any;
}

const WalletInfo: React.FC<WalletInfoProps> = ({
  userWallet,
  isLoadingWallet,
  selectedUser
}) => {
  return (
    <Card>
      <CardHeader title="Wallet Information" />

      {isLoadingWallet ? (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">Loading wallet information...</Text>
        </Box>
      ) : selectedUser ? (
        userWallet ? (
          <WalletGrid>
            <BalanceCard>
              <Text variant="h1" fontWeight="bold" marginBottom="sm">
                ${userWallet.params?.balance || '0.00'}
              </Text>
              <Text variant="lg">
                Current Balance
              </Text>
            </BalanceCard>

            <Box display="flex" flexDirection="column" gap="default">
              <InfoItem>
                <Text fontWeight="bold" color="grey60" variant="sm">Wallet ID</Text>
                <Text>{userWallet.id}</Text>
              </InfoItem>

              <InfoItem>
                <Text fontWeight="bold" color="grey60" variant="sm">Status</Text>
                <Text color={userWallet.params?.status === 'ACTIVE' ? 'success' : 'error'}>
                  {userWallet.params?.status || 'Unknown'}
                </Text>
              </InfoItem>

              <InfoItem>
                <Text fontWeight="bold" color="grey60" variant="sm">Last Updated</Text>
                <Text>
                  {userWallet.params?.updatedAt
                    ? new Date(userWallet.params.updatedAt).toLocaleString()
                    : 'Never'
                  }
                </Text>
              </InfoItem>
            </Box>
          </WalletGrid>
        ) : (
          <Box textAlign="center" padding="xl">
            <Text color="grey60">No wallet found for this user</Text>
          </Box>
        )
      ) : (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">Select a user to see wallet details</Text>
        </Box>
      )}
    </Card>
  );
};

export default WalletInfo;
