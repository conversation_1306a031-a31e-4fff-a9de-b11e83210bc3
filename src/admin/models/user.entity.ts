import { Entity, Column, PrimaryGeneratedColumn, BaseEntity } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

export enum UserStatus {
  Active = 'Active',
  Inactive = 'Inactive',
  Closed = 'Closed',
}

@Entity('tblclients')
export class User extends BaseEntity  {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({type: 'varchar', nullable: false})
  uuid: string;

  @Column({name: 'email_verified', type: 'int', width: 1, nullable: false, default: 0})
  emailVerified: number;

  @Column({type: 'varchar', nullable: false})
  email: string;

  @Column({type: 'varchar', nullable: false})
  password: string;

  @Column({type: 'varchar', nullable: true})
  firstname: string;

  @Column({type: 'varchar', nullable: true})
  lastname: string;

  @Column({type: 'varchar', nullable: true})
  language: string;

  @Column({type: 'enum', nullable: false, enum: UserStatus})
  status: UserStatus;

  @Column({type: 'date', nullable: false})
  datecreated: Date;

  @Column({type: 'varchar', nullable: true})
  notes: string;

  @Column({name: 'created_at', type: 'timestamp', nullable: false})
  createdAt: Date;

  @Column({name: 'updated_at', type: 'timestamp', nullable: false})
  updatedAt: Date;

  @Column({name: 'marketing_emails_opt_in', type: 'boolean', nullable: false, default: 1})
  marketingOptIn: boolean;

  @Column({name: 'credit', type: 'decimal', precision: 10, scale: 2, default: 0})
  credit: number;

  @Column({name: 'lastlogin', nullable: true})
  lastLogin: string;

  @Column({name: 'taxexempt', type: 'int', width: 1, nullable: true, default: 0})
  allowAppleIAP?: number;

  async hashPassword(password: string): Promise<string> {
    // whmcs uses 12 rounds
    const salt = await bcrypt.genSalt(12);
    return await bcrypt.hash(password, salt);
  }

  generateUUID(): string {
    return uuidv4();
  }

  async toJson() {
    return {
      id: this.id,
      uuid: this.uuid,
      email : this.email,
      firstname: this.firstname,
      lastname: this.lastname,
      emailVerified: this.emailVerified,
      datecreated: this.datecreated,
      language: this.language,
      affid: this.notes,
      marketingOptIn: this.marketingOptIn,
      locale: this.language,
      allowAppleIAP: this.allowAppleIAP === 1,
    };
  }
}