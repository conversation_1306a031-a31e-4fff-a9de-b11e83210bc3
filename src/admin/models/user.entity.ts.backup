import { Entity, Column, PrimaryGeneratedColumn, BaseEntity } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

export enum UserStatus {
  Active = 'Active',
  Inactive = 'Inactive',
  Closed = 'Closed',
}

@Entity('tblclients')
export class User extends BaseEntity  {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({type: 'varchar', nullable: false})
  uuid: string;

  @Column({name: 'email_verified', type: 'int', width: 1, nullable: false, default: 0})
  emailVerified: number;

  @Column({type: 'varchar', nullable: false})
  email: string;

  @Column({type: 'varchar', nullable: false})
  password: string;

  @Column({type: 'varchar', nullable: true})
  firstname: string;

  @Column({type: 'varchar', nullable: true})
  lastname: string;

  @Column({type: 'varchar', nullable: true})
  language: string;

  @Column({type: 'varchar', nullable: true})
  country: string;

  @Column({type: 'enum', nullable: false, enum: UserStatus})
  status: UserStatus;

  @Column({type: 'date', nullable: false})
  datecreated: Date;

  @Column({type: 'varchar', nullable: true})
  notes: string;

  @Column({name: 'created_at', type: 'date', nullable: false})
  createdAt: Date;

  @Column({name: 'updated_at', type: 'date', nullable: false})
  updatedAt: Date;

  @Column({name: 'marketing_emails_opt_in', type: 'boolean', nullable: false, default: 1})
  marketingOptIn: boolean;

  @Column({name: 'companyname', nullable: false})
  companyName: string;

  @Column()
  address1: string;

  @Column()
  address2: string;

  @Column()
  city: string;

  @Column()
  state: string;

  @Column({name: 'postcode'})
  postCode: string;

  @Column({name: 'phonenumber'})
  phoneNumber: string;

  @Column({name: 'tax_id'})
  taxId: string;

  @Column({name: 'authmodule'})
  authModule: string;

  @Column({name: 'authdata'})
  authData: string;

  @Column()
  currency: number;

  @Column({name: 'defaultgateway'})
  defaultGateway: string;

  @Column({name: 'credit', type: 'decimal', precision: 10, scale: 2, default: 0})
  credit: number;

  @Column({name: 'taxexempt', type: 'int', width: 1, nullable: true, default: 0})
  taxExempt: number;

  // @Column({name: 'latefeeoveride', type: 'int', width: 1, nullable: false, default: 0})
  // lateFeeOveride: number;

  // @Column({name: 'overideduenotices', type: 'int', width: 1, nullable: false, default: 0})
  // overideDueNotices: number;

  // @Column({name: 'separateinvoices', type: 'int', width: 1, nullable: false, default: 0})
  // separateInvoices: number;

  // @Column({name: 'disableautocc', type: 'int', width: 1, nullable: false, default: 0})
  // disableAutoCC: number;

  @Column({name: 'securityqid'})
  securityQid: number;

  @Column({name: 'securityqans'})
  securityQans: string;

  @Column({name: 'groupid'})
  groupId: number;

  @Column({name: 'cardtype'})
  cardType: string;

  @Column({name: 'cardlastfour'})
  cardLastFour: string;

  @Column({name: 'cardnum'})
  cardNum: string;

  @Column({name: 'startdate'})
  startDate: string;

  @Column({name: 'expdate'})
  expDate: string;

  @Column({name: 'issuenumber'})
  issueNumber: string;

  @Column({name: 'bankname'})
  bankName: string;

  @Column({name: 'banktype'})
  bankType: string;

  @Column({name: 'bankcode'})
  bankCode: string;

  @Column({name: 'bankacct'})
  bankAcct: string;

  @Column({name: 'gatewayid'})
  gatewayId: string;

  @Column({name: 'lastlogin'})
  lastLogin: string;

  @Column({name: 'ip'})
  ip: string;

  @Column({name: 'host'})
  host: string;

  @Column({name: 'pwresetkey'})
  pwResetKey: string;

  @Column({name: 'pwresetexpiry'})
  pwResetExpiry: Date;

  @Column({name: 'emailoptout', type: 'tinyint', width: 1, nullable: false, default: 0})
  emailOptOut: number;

  @Column({name: 'overrideautoclose', type: 'int', width: 1})
  overrideAutoClose: number;

  @Column({name: 'allow_sso', type: 'tinyint', width: 1, nullable: false, default: 0})
  allowSso: number;

  @Column({name: 'email_preferences'})
  emailPreferences: string;

  @Column({name: 'billingcid'})
  billingCid: number;

  async hashPassword(password: string): Promise<string> {
    // whmcs uses 12 rounds
    const salt = await bcrypt.genSalt(12);
    return await bcrypt.hash(password, salt);
  }

  generateUUID(): string {
    return uuidv4();
  }

  async toJson() {
    return {
      id: this.id,
      uuid: this.uuid,
      email : this.email,
      firstname: this.firstname,
      lastname: this.lastname,
      emailVerified: this.emailVerified,
      datecreated: this.datecreated,
      language: this.language,
      affid: this.notes,
      marketingOptIn: this.marketingOptIn,
      locale: this.language,
      allowAppleIAP: this.taxExempt === 1,
    };
  }
}