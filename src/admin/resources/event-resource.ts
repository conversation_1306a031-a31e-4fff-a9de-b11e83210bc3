import { Event } from "../models/events.entity.js";
import { ResourceWithOptions } from "adminjs";

export const EventResource: ResourceWithOptions = {
  resource: Event,
  options: {
    // 默认排序：ID 降序
    sort: {
      sortBy: 'id',
      direction: 'desc'
    },
    // 自定义列表页面字段显示顺序
    listProperties: [
      'id',
      'userId',
      'type',
      'createdAt'
    ],
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      userId: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'reference',
        reference: 'User'
      },
      type: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      createdAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime'
      }
    }
  }
};
