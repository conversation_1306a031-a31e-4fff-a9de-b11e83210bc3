import { InvoiceItem } from "../models/invoice.entity.js";
import { ResourceWithOptions } from "adminjs";
import { Components } from '../component-loader.js';

export const InvoiceItemResource: ResourceWithOptions = {
  resource: InvoiceItem,
  options: {
    // 默认排序：ID 降序
    sort: {
      sortBy: 'id',
      direction: 'desc'
    },
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      invoiceid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'reference',
        reference: 'Invoice'
      },
      userid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'reference',
        reference: 'User',
        components: {
          list: Components.UserReference
          // 在 show 视图中使用默认的引用显示，会显示标签
        }
      },
      relid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'reference',
        reference: 'Service',
        description: 'Related service ID. Leave empty (0) if not related to any service.',
        components: {
          list: Components.ServiceReference
          // 在 show 视图中使用默认的引用显示，会显示标签
        }
      },
      type: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
        availableValues: [
          { value: 'Hosting', label: 'Hosting' },
          { value: 'Upgrade', label: 'Upgrade' },
          { value: 'Flow', label: 'Flow' },
          { value: 'Balance', label: 'Balance' },
          { value: 'Trial', label: 'Trial' },
          { value: 'Other', label: 'Other' }
        ]
      },
      description: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      amount: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'number'
      },
      duedate: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'date'
      },
      paymentmethod: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'string'
      },
      taxed: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'boolean'
      },
      notes: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'textarea'
      }
    },
    actions: {
      new: {
        before: async (request: any) => {
          // 设置默认值
          if (request.payload) {
            // 如果没有设置 relid，使用默认值 0
            if (!request.payload.relid) {
              request.payload.relid = 0;
            }

            // 如果没有设置 duedate，使用当前日期
            if (!request.payload.duedate) {
              request.payload.duedate = new Date().toISOString().split('T')[0];
            }

            // 如果没有设置 paymentmethod，使用空字符串
            if (!request.payload.paymentmethod) {
              request.payload.paymentmethod = '';
            }

            // 设置其他可能缺失的字段默认值
            if (request.payload.taxed === undefined) {
              request.payload.taxed = false;
            }

            if (!request.payload.notes) {
              request.payload.notes = '';
            }
          }

          return request;
        },
        after: async (response: any) => {
          // 创建成功后的处理
          if (response.record) {
            return {
              ...response,
              notice: {
                message: 'Invoice item created successfully',
                type: 'success'
              }
            };
          }
          return response;
        },
      },
      edit: {
        after: async (response: any, request: any) => {
          // 阻止跳转到列表页面，保留在当前编辑页面
          if (request.method === 'post' && response.record) {
            return {
              ...response,
              redirectUrl: null,
              notice: {
                message: 'Invoice item updated successfully',
                type: 'success'
              }
            };
          }
          return response;
        },
      },
    }
  }
};
