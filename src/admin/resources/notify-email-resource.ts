import { NotifyEmail } from "../models/notify-email.entity.js";
import { ResourceWithOptions } from "adminjs";

export const NotifyEmailResource: ResourceWithOptions = {
  resource: NotifyEmail,
  options: {
    // 默认排序：ID 降序
    sort: {
      sortBy: 'id',
      direction: 'desc'
    },
    // 自定义列表页面字段显示顺序
    listProperties: [
      'id',
      'subject',
      'type',
      'sender',
      'receiver',
      'status',
      'createdAt',
      'updatedAt'
    ],
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      subject: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      type: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      content: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'textarea'
      },
      templateId: {
        isVisible: { list: false, filter: true, show: true, edit: true },
        type: 'number'
      },
      templateParam: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'textarea'
      },
      sender: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'number'
      },
      receiver: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'number'
      },
      status: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      createdAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime'
      },
      updatedAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime'
      }
    }
  }
};
