import { Pricing } from "../models/pricing.entity.js";
import { ResourceWithOptions } from "adminjs";

export const PricingResource: ResourceWithOptions = {
  resource: Pricing,
  options: {
    // 默认排序：ID 降序
    sort: {
      sortBy: 'id',
      direction: 'desc'
    },
    // 自定义列表页面字段显示顺序
    listProperties: [
      'id',
      'type',
      'relid',
      'monthly',
      'quarterly',
      'semiannually',
      'annually',
      'biennially',
      'triennially'
    ],
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      type: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      relid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'reference',
        reference: 'Product'
      },
      monthly: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      quarterly: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      semiannually: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      annually: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      biennially: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      triennially: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      }
    }
  }
};
