import { Product } from "../models/product.entity.js";
import { ResourceWithOptions } from "adminjs";

export const ProductResource: ResourceWithOptions = {
  resource: Product,
  options: {
    // 默认排序：ID 降序
    sort: {
      sortBy: 'id',
      direction: 'desc'
    },
    // 自定义列表页面字段显示顺序
    listProperties: [
      'id',
      'name',
      'gid',
      'description',
      'hidden'
    ],
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      name: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      gid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'number'
      },
      description: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      hidden: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      }
    }
  }
};
