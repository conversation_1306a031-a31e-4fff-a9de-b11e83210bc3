import { Service } from "../models/service.entity.js";
import { SuperapiService } from "./superapi-service.js";
import { ResourceWithOptions } from "adminjs";
import { Components } from '../component-loader.js';

const superapiService = new SuperapiService();

export const ServiceResource: ResourceWithOptions = {
    resource: Service,
    options: {
      // 默认排序：ID 降序
      sort: {
        sortBy: 'id',
        direction: 'desc'
      },
      // 自定义详情页面字段显示顺序
      showProperties: [
        'id',
        'userid',
        'packageid',
        'domainstatus',
        'billingcycle',
        'regdate',
        'nextduedate',
        'firstpaymentamount',
        'amount',
        'username',
        'password',
        'notes',
        // 关联数据 - 放在最后
        'serviceInvoices'
      ],
      properties: {
        id: {
          isVisible: { list: true, filter: true, show: true, edit: false },
          type: 'number',
          isTitle: true
        },
        userid: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'reference',
          reference: 'User'
        },
        packageid: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'reference',
          reference: 'Product'
        },
        billingcycle: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'string'
        },
        regdate: {
          isVisible: { list: true, filter: true, show: true, edit: false },
          type: 'date'
        },
        nextduedate: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'datetime'
        },
        domainstatus: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'string',
          availableValues: [
            { value: 'Pending', label: 'Pending' },
            { value: 'Active', label: 'Active' },
            { value: 'Suspended', label: 'Suspended' },
            { value: 'Terminated', label: 'Terminated' },
            { value: 'Cancelled', label: 'Cancelled' },
            { value: 'Fraud', label: 'Fraud' }
          ]
        },
        firstpaymentamount: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'number'
        },
        amount: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'number'
        },
        username: {
          isVisible: { list: false, filter: false, show: true, edit: true },
          type: 'string'
        },
        password: {
          isVisible: { list: false, filter: false, show: false, edit: true },
          type: 'password'
        },
        notes: {
          isVisible: { list: false, filter: false, show: true, edit: true },
          type: 'textarea'
        },
        serviceInvoices: {
          isVisible: { list: false, filter: false, show: true, edit: false },
          type: 'string',
          components: {
            show: Components.ServiceInvoices
          }
        }
      },
      actions: {
        edit: {
          after: async (response, request, context) => {
            const updatedRecord = response.record;
            if (request.payload.packageid) {
              await superapiService.syncServicePackage(updatedRecord.params.id, +request.payload.packageid);
            } 
            
            if (request.payload.nextduedate) {
              await superapiService.syncServiceExpireDate(updatedRecord.params.id, request.payload.nextduedate);
            }
            
            // 阻止跳转到列表页面，保留在当前编辑页面
            return {
              ...response,
              redirectUrl: null,
              notice: {
                message: 'Service information updated successfully',
                type: 'success'
              }
            };
          },
        },
        superapi: {
          actionType: 'record',
          component: Components.SuperAPI,
          handler: async (request, response, context) => {
            // Try to extract serviceId from multiple possible locations
            const serviceId =
              context.record?.param?.('id') ||
              context.record?.params?.id ||
              request.params?.recordId ||
              request.payload?.serviceId ||
              request.payload?.recordIds?.[0] ||
              request.query?.serviceId;

            if (!serviceId) {
              throw new Error('Service ID is required but not provided');
            }

            const service = await superapiService.fetchService({ serviceId: serviceId });

            // Create a basic record structure if context.record is not available
            // This happens when the component is used on dashboard rather than in a record context
            let recordJSON;
            
            if (context.record) {
              // If we have a record context, use it
              recordJSON = context.record.toJSON(context.currentAdmin);
            } else {
              // Create a minimal record structure for dashboard usage
              recordJSON = {
                id: serviceId,
                params: {
                  id: serviceId
                },
                populated: {},
                errors: {}
              };
            }

            // 将 SuperAPI 数据添加到 record 的 params 中
            recordJSON.params = {
              ...recordJSON.params,
              superapiData: service
            };

            return {
              record: recordJSON
            };
          },
        },
        editSuperapi: {
          actionType: 'record',
          component: Components.EditSuperAPI,
          handler: async (request, response, context) => {
            const serviceId =
              context.record?.param?.('id') ||
              context.record?.params?.id ||
              request.params?.recordId ||
              request.query?.serviceId;

            const service = await superapiService.fetchService({ serviceId: serviceId });

            // Create record JSON object or create a minimal one
            let recordJSON;
            
            if (context.record) {
              recordJSON = context.record.toJSON(context.currentAdmin);
            } else {
              // Create a minimal record structure when context.record is not available
              recordJSON = {
                id: serviceId,
                params: {
                  id: serviceId
                },
                populated: {},
                errors: {}
              };
            }

            // 将 SuperAPI 数据添加到 record 的 params 中
            recordJSON.params = {
              ...recordJSON.params,
              superapiData: service
            };

            return {
              record: recordJSON
            };
          },
        },
        updateSuperapi: {
          actionType: 'record',
          showInDrawer: false,
          isVisible: false,
          isAccessible: () => true,
          handler: async (request, response, context) => {
            const serviceId =
              context.record?.params?.id ||
              request.params?.recordId;

            if (!serviceId) {
              throw new Error('Service ID not found');
            }

            const updateData = request.payload;

            try {
              await superapiService.updateService(serviceId, updateData);

              return {
                record: context.record?.toJSON(context.currentAdmin),
                notice: {
                  message: 'SuperAPI information updated successfully!',
                  type: 'success'
                },
                redirectUrl: null // 阻止跳转，保留在当前页面
              };
            } catch (error) {
              console.error('Error updating SuperAPI:', error);
              throw new Error('Failed to update SuperAPI information');
            }
          },
        },
        resetSuperapi: {
          actionType: 'record',
          showInDrawer: false,
          isVisible: false,
          isAccessible: () => true,
          handler: async (request, response, context) => {
            const serviceId =
              context.record?.params?.id ||
              request.params?.recordId;

            if (!serviceId) {
              throw new Error('Service ID not found');
            }

            try {
              // 执行重置
              await superapiService.reset(serviceId);
              
              // 重新获取最新的数据
              let updatedService;
              try {
                updatedService = await superapiService.fetchService({ serviceId: serviceId });
              } catch (e) {
                console.error('Error fetching updated SuperAPI data after reset:', e);
              }
              
              // 准备返回数据
              let recordJSON;
              if (context.record) {
                recordJSON = context.record.toJSON(context.currentAdmin);
                // 更新 record 中的 superapiData
                if (updatedService) {
                  recordJSON.params = {
                    ...recordJSON.params,
                    superapiData: updatedService
                  };
                }
              } else {
                recordJSON = {
                  id: serviceId,
                  params: {
                    id: serviceId,
                    ...(updatedService && { superapiData: updatedService })
                  },
                  populated: {},
                  errors: {}
                };
              }

              return {
                record: recordJSON,
                notice: {
                  message: 'SuperAPI service reset successfully!',
                  type: 'success'
                },
                redirectUrl: null // 阻止跳转，保留在当前页面
              };
            } catch (error) {
              console.error('Error resetting SuperAPI:', error);
              
              // 返回更具描述性的错误消息
              if (error.message && error.message.includes('403')) {
                throw new Error('重置失败: 没有权限访问此资源');
              } else if (error.message && error.message.includes('404')) {
                throw new Error('资源未找到: 找不到指定的 SuperAPI 服务');
              } else {
                throw new Error(`重置流量失败: ${error.message}`);
              }
            }
          },
        },
        redeemSuperapi: {
          actionType: 'record',
          showInDrawer: false,
          isVisible: false,
          isAccessible: () => true,
          handler: async (request, response, context) => {
            const serviceId =
              context.record?.params?.id ||
              request.params?.recordId;

            const delta = request.payload?.delta;

            if (!serviceId) {
              throw new Error('Service ID not found');
            }

            if (!delta || isNaN(delta)) {
              throw new Error('Invalid redeem amount');
            }

            try {
              // 获取 SuperAPI 数据以找到内部 ID
              const superapiData = context.record?.params?.superapiData;
              // 尝试获取 SuperAPI 内部 ID
              const superapiId = superapiData?.id;
              
              // 如果没有找到 superapiId，需要先获取 SuperAPI 数据
              let idToUse = superapiId;
              if (!idToUse) {
                try {
                  const service = await superapiService.fetchService({ serviceId: serviceId });
                  idToUse = service?.id;
                } catch (e) {
                  console.error('Error fetching SuperAPI data for redeem:', e);
                }
              }
              
              // 执行兑换
              await superapiService.redeem(serviceId, delta, idToUse);
              
              // 重新获取最新的数据
              let updatedService;
              try {
                updatedService = await superapiService.fetchService({ serviceId: serviceId });
              } catch (e) {
                console.error('Error fetching updated SuperAPI data after redeem:', e);
              }
              
              // 准备返回数据
              let recordJSON;
              if (context.record) {
                recordJSON = context.record.toJSON(context.currentAdmin);
                // 更新 record 中的 superapiData
                if (updatedService) {
                  recordJSON.params = {
                    ...recordJSON.params,
                    superapiData: updatedService
                  };
                }
              } else {
                recordJSON = {
                  id: serviceId,
                  params: {
                    id: serviceId,
                    ...(updatedService && { superapiData: updatedService })
                  },
                  populated: {},
                  errors: {}
                };
              }

              return {
                record: recordJSON,
                notice: {
                  message: `Successfully redeemed ${delta} bytes from SuperAPI service!`,
                  type: 'success'
                },
                redirectUrl: null // 阻止跳转，保留在当前页面
              };
            } catch (error) {
              console.error('Error redeeming SuperAPI:', error);
              
              // 返回更具描述性的错误消息
              if (error.message && error.message.includes('403')) {
                throw new Error('兑换失败: 没有权限访问此资源');
              } else if (error.message && error.message.includes('404')) {
                throw new Error('资源未找到: 找不到指定的 SuperAPI 服务');
              } else {
                throw new Error(`兑换流量失败: ${error.message}`);
              }
            }
          },
        },
        updateTrafficSuperapi: {
          actionType: 'record',
          showInDrawer: false,
          isVisible: false,
          isAccessible: () => true,
          handler: async (request, response, context) => {
            const serviceId =
              context.record?.params?.id ||
              request.params?.recordId;
              
            const { username, password, upload, download } = request.payload || {};

            if (!serviceId) {
              throw new Error('Service ID not found');
            }

            if (!username || !password) {
              throw new Error('Username and password are required');
            }

            if ((upload !== undefined && isNaN(Number(upload))) || 
                (download !== undefined && isNaN(Number(download)))) {
              throw new Error('Upload and download must be numbers');
            }

            try {
              // Get the SuperAPI data for this service to find the internal SuperAPI ID
              const superapiData = context.record?.params?.superapiData;
              
              // 尝试获取 SuperAPI 内部 ID
              const superapiId = superapiData?.id;
              
              // 如果没有找到 superapiId，需要先获取 SuperAPI 数据
              let idToUse = superapiId;
              if (!idToUse) {
                try {
                  const service = await superapiService.fetchService({ serviceId: serviceId });
                  idToUse = service?.id;
                } catch (e) {
                  console.error('Error fetching SuperAPI data:', e);
                }
              }
              
              // 如果获取到了 SuperAPI 内部 ID，就使用它，否则回退到使用 serviceId
              const result = await superapiService.updateTrafficUsage(
                idToUse, 
                username, 
                password, 
                Number(upload) || 0, 
                Number(download) || 0
              );
              
              // 再次获取最新的 SuperAPI 数据，以确保显示更新后的流量信息
              let updatedService;
              try {
                updatedService = await superapiService.fetchService({ serviceId: serviceId });
              } catch (e) {
                console.error('Error fetching updated SuperAPI data:', e);
                // 即使这里错误也继续执行，不影响主流程
              }
              
              // Create a record JSON object or use the existing one
              let recordJSON;
              
              if (context.record) {
                recordJSON = context.record.toJSON(context.currentAdmin);
                
                // 更新 record 中的 superapiData
                if (updatedService) {
                  recordJSON.params = {
                    ...recordJSON.params,
                    superapiData: updatedService
                  };
                }
              } else {
                // Create a minimal record structure when context.record is not available
                recordJSON = {
                  id: serviceId,
                  params: {
                    id: serviceId,
                    ...(updatedService && { superapiData: updatedService })
                  },
                  populated: {},
                  errors: {}
                };
              }

              return {
                record: recordJSON,
                notice: {
                  message: 'Traffic usage updated successfully!',
                  type: 'success'
                },
                redirectUrl: null // 阻止跳转，保留在当前页面
              };
            } catch (error) {
              console.error('Error updating traffic usage:', error);
              
              // 返回更具描述性的错误消息
              if (error.message && error.message.includes('403')) {
                throw new Error('认证失败: 用户名或密码不正确，或者没有权限访问此资源');
              } else if (error.message && error.message.includes('404')) {
                throw new Error('资源未找到: 找不到指定的 SuperAPI 服务');
              } else if (error.message && error.message.includes('SuperAPI ID is required')) {
                throw new Error('无法获取 SuperAPI ID: 请先确保已获取 SuperAPI 服务数据');
              } else {
                throw new Error(`更新流量失败: ${error.message}`);
              }
            }
          },
        },
      },
    },
  };