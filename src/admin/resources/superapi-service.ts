import { ReqCreateSuperapiService, ReqFindSuperapiService, SuperapiProductService } from "./superapi-types.js";
import { ConfigService } from '../../config.service.js';

export class SuperapiService {
    private readonly productName: string;
    private readonly apiHost: string;
  
    constructor() {
      const config = new ConfigService(`${process.env.NODE_ENV || 'dev'}.env`);
      this.productName = config.get('PRODUCT_NAME');
      this.apiHost = config.get('SUPERAPI_HOST');

    }
  
    private async handleRequest<T>(
      url: string,
      options: RequestInit,
      errorConfig: { error: string; message: string }
    ): Promise<T> {
      try {
        const response = await fetch(url, {
          ...options,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...options.headers,
          },
        });
  
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
  
        const contentType = response.headers.get('content-type');
        if (contentType?.includes('application/json')) {
          return await response.json();
        }
        return await response.text() as T;
      } catch (e) {
        console.error(`${errorConfig.error}: ${(e as Error).message}`);
        throw new Error(errorConfig.message);
      }
    }
  
    async fetchService(body: ReqFindSuperapiService): Promise<SuperapiProductService> {
      if (!body.serviceId) {
        console.error('FetchServiceError: serviceId is undefined or null');
        throw new Error('ServiceId is required');
      }
      
      const response = await this.handleRequest<SuperapiProductService>(
        `${this.apiHost}/api/v1/services/show`,
        {
          method: 'POST',
          body: JSON.stringify({
            clientId: body.serviceId,
            client: this.productName,
          }),
        },
        {
          error: 'FetchServiceError',
          message: 'Network error, can not fetch service',
        }
      );
      
      // 确保响应对象包含 id
      if (!response.id) {
        console.warn('SuperAPI service response does not include id field:', response);
      }
      
      return response;
    }
  
    async createService(body: ReqCreateSuperapiService): Promise<boolean> {
      const result = await this.handleRequest<SuperapiProductService>(
        `${this.apiHost}/api/v1/services`,
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        {
          error: 'CreateServiceError',
          message: 'Network error, can not create service',
        }
      );
      return result.clientId === body.clientId;
    }
  
    async fetchSubscription(client: string, id: number, binary?: string, isSelfDevClient?: string): Promise<string> {
      const params = new URLSearchParams({
        agent: client,
        ...(binary && { binary: 'yes' }),
        ...(isSelfDevClient && { isSelfDevClient: 'yes' }),
      });
  
      return this.handleRequest<string>(
        `${this.apiHost}/api/v1/services/${this.productName}/${id}/subscription?${params}`,
        { method: 'GET' },
        {
          error: 'FetchSubscriptionError',
          message: 'Network error, can not fetch subscription',
        }
      );
    }
  
    packageId2packageInfo(packageId: number) {
      if (packageId === 33 || packageId === 2) {
        return JSON.stringify([{traffic: 107374182400, speed: 2621440}]);
      }
      if (packageId === 34 || packageId === 3) {
        return JSON.stringify([{traffic: 214748364800, speed: 3932160}]);
      }
      return JSON.stringify([{traffic: 53687091200, speed: 1310720}]);
    }
  
    async syncServicePackage(serviceId: number, packageId: number): Promise<string> {
      return this.handleRequest<string>(
        `${this.apiHost}/api/v1/services/update`,
        {
          method: 'POST',
          body: JSON.stringify({
            clientId: serviceId,
            client: this.productName,
            quota: this.packageId2packageInfo(packageId),
          }),
        },
        {
          error: 'SyncPackageError',
          message: 'Network error, can not sync package',
        }
      );
    }
  
    async getClusterInfos(): Promise<string> {
      return this.handleRequest<string>(
        `${this.apiHost}/api/v1/services/infos`,
        { method: 'GET' },
        {
          error: 'FetchNodeInfosError',
          message: 'Network error, can not fetch node infos',
        }
      );
    }
  
    async syncServiceExpireDate(serviceId: number, nextDueDate: Date): Promise<string> {
      return this.handleRequest<string>(
        `${this.apiHost}/api/v1/services/update`,
        {
          method: 'POST',
          body: JSON.stringify({
            clientId: serviceId,
            client: this.productName,
            expiredAt: nextDueDate,
          }),
        },
        {
          error: 'SyncExpireDateError',
          message: 'Network error, can not sync expire date',
        }
      );
    }
  
    async redeem(serviceId: number | string, delta: number | string, superapiId?: number): Promise<any> {
      // 使用 superapiId (内部 ID) 如果提供了，否则使用 serviceId
      const id = superapiId || serviceId;
      
      try {
        const result = await this.handleRequest(
          `${this.apiHost}/api/v1/services/${this.productName}/${id}/reduceUsage`,
          {
            method: 'POST',
            body: JSON.stringify({ delta }),
          },
          {
            error: 'RedeemDataError',
            message: 'Network error, can not redeem data',
          }
        );
        
        console.log('Redeem result:', result);
        return result;
      } catch (error) {
        console.error(`RedeemDataError: ${error.message}`);
        throw new Error(`Failed to redeem data for ID ${id}: ${error.message}`);
      }
    }
  
    async reset(serviceId: number | string): Promise<string> {
      try {
        const result = await this.handleRequest<string>(
          `${this.apiHost}/api/v1/services/reset`,
          {
            method: 'POST',
            body: JSON.stringify({
              client: this.productName,
              clientId: serviceId,
            }),
          },
          {
            error: 'ResetServiceError',
            message: 'Network error, can not reset this service',
          }
        );
        
        console.log('Reset result:', result);
        return result;
      } catch (error) {
        console.error(`ResetServiceError: ${error.message}`);
        throw new Error(`Failed to reset service for ID ${serviceId}: ${error.message}`);
      }
    }

    async updateService(serviceId: number | string, updateData: any): Promise<string> {
      return this.handleRequest<string>(
        `${this.apiHost}/api/v1/services/update`,
        {
          method: 'POST',
          body: JSON.stringify({
            clientId: serviceId,
            client: this.productName,
            ...updateData,
          }),
        },
        {
          error: 'UpdateServiceError',
          message: 'Network error, can not update this service',
        }
      );
    }

    async updateTrafficUsage(superapiId: number | string, username: string, password: string, upload: number, download: number): Promise<any> {
      if (!superapiId) {
        throw new Error('SuperAPI ID is required for updating traffic usage');
      }
      
      try {
        const result = await this.handleRequest(
          `${this.apiHost}/api/v1/subscribes/${superapiId}`,
          {
            method: 'PUT',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              username,
              password,
              upload,
              download
            }),
          },
          {
            error: 'UpdateTrafficError',
            message: 'Network error, cannot update traffic usage',
          }
        );
        
        return result;
      } catch (error) {
        console.error(`UpdateTrafficError: ${error.message}`);
        // 重新抛出错误，添加更多上下文信息
        throw new Error(`Failed to update traffic usage for ID ${superapiId}: ${error.message}`);
      }
    }
  }