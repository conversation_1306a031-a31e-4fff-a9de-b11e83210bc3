import { Wallet } from "../models/wallet.entity.js";
import { ResourceWithOptions } from "adminjs";

export const WalletResource: ResourceWithOptions = {
  resource: Wallet,
  options: {
    // 默认排序：ID 降序
    sort: {
      sortBy: 'id',
      direction: 'desc'
    },
    // 自定义列表页面字段显示顺序
    listProperties: [
      'id',
      'userId',
      'status',
      'balance',
      'address',
      'createdAt',
      'updatedAt'
    ],
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      userId: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'reference',
        reference: 'User'
      },
      status: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      balance: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'number'
      },
      address: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      createdAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime'
      },
      updatedAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime'
      }
    }
  }
};
