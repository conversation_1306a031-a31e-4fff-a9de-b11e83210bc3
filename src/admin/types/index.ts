// Common types for AdminJS Dashboard components

export interface User {
  id: string;
  params: {
    email?: string;
    createdAt?: string;
    emailVerified?: boolean;
    cardLastFour?: string;
    gatewayId?: string;
    lastLogin?: string;
    ip?: string;
    host?: string;
    [key: string]: any;
  };
}

export interface Service {
  id: string;
  params: {
    name?: string;
    domainstatus?: string;
    packageid?: string;
    nextduedate?: string;
    billingcycle?: string;
    [key: string]: any;
  };
}

export interface Product {
  id: string;
  params: {
    name?: string;
    [key: string]: any;
  };
}

export interface Pricing {
  params: {
    relid?: string;
    [key: string]: any;
  };
}

export interface Invoice {
  id: string;
  params: {
    subtotal?: string;
    duedate?: string;
    status?: string;
    [key: string]: any;
  };
}

export interface Event {
  id: string;
  params: {
    createdAt?: string;
    type?: string;
    context?: string;
    [key: string]: any;
  };
}

export interface NotifyEmail {
  id: string;
  params: {
    subject?: string;
    createdAt?: string;
    templateParam?: string;
    error?: string;
    [key: string]: any;
  };
}

export interface Wallet {
  id: string;
  params: {
    balance?: string;
    status?: string;
    updatedAt?: string;
    [key: string]: any;
  };
}
