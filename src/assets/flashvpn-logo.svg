<svg width="120" height="40" viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="flashGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Flash icon -->
  <path d="M8 8 L16 20 L12 20 L20 32 L12 20 L16 20 L8 8" fill="url(#flashGradient)" stroke="#ffffff" stroke-width="0.5"/>
  
  <!-- VPN shield -->
  <path d="M24 12 L30 8 L36 12 L36 24 C36 28 30 32 30 32 C30 32 24 28 24 24 L24 12" fill="url(#flashGradient)" stroke="#ffffff" stroke-width="0.5"/>
  <circle cx="30" cy="20" r="3" fill="#ffffff"/>
  
  <!-- Text -->
  <text x="42" y="16" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#333333">Flash</text>
  <text x="42" y="28" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#7c3aed">VPN</text>
</svg>
