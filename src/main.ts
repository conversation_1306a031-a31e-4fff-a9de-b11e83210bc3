import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

import { AppModule } from './app.module.js';

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  
  // Serve static files for AdminJS assets
  app.useStaticAssets(join(__dirname, 'assets'), {
    prefix: '/assets/',
  });
  
  await app.listen(process.env.PORT ?? 3002);
}
bootstrap();
